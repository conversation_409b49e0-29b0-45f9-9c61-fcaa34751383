<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Order Placed Successfully - Luxe Scents</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
    :root {
      --primary-color: #6200ea;
      --secondary-color: #f8f9fa;
      --success-color: #28a745;
      --danger-color: #dc3545;
      --warning-color: #ffc107;
      --info-color: #17a2b8;
      --dark-color: #343a40;
      --light-color: #f8f9fa;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: var(--dark-color);
    }

    .navbar {
      background-color: rgba(0, 0, 0, 0.9);
      padding: 1rem 2rem;
      border-bottom: 1px solid #090909;
      backdrop-filter: blur(10px);
    }
    .navbar-brand {
      font-weight: 700;
      font-size: 1.8rem;
      color: #ffffff !important;
      letter-spacing: 1px;
    }
    .navbar-brand:hover {
      color: #bb86fc !important;
    }
    .nav-link {
      color: #e0e0e0 !important;
      font-weight: 500;
      margin: 0 1rem;
      transition: color 0.3s;
    }
    .nav-link:hover {
      color: #bb86fc !important;
    }
    .navbar-icons {
      display: flex;
      align-items: center;
      gap: 1.5rem;
    }
    .navbar-icons a {
      color: #e0e0e0;
      font-size: 1.2rem;
      position: relative;
      transition: color 0.3s;
    }
    .navbar-icons a:hover {
      color: #bb86fc;
    }
    .badge {
      position: absolute;
      top: -8px;
      right: -8px;
      background-color: #6200ea;
      color: #ffffff;
      font-size: 0.7rem;
      border-radius: 50%;
      padding: 2px 6px;
    }
    .dropdown-menu {
      background-color: #2a2a2a;
      border: 1px solid #444;
    }
    .dropdown-item {
      color: #e0e0e0 !important;
      transition: background-color 0.3s, color 0.3s;
    }
    .dropdown-item:hover {
      background-color: #6200ea;
      color: #ffffff !important;
    }

    .success-container {
      max-width: 800px;
      margin: 3rem auto;
      padding: 0 1rem;
    }

    .success-card {
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      padding: 3rem;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .success-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 5px;
      background: linear-gradient(90deg, var(--success-color), var(--primary-color));
    }

    .success-illustration {
      width: 200px;
      height: 200px;
      margin: 0 auto 2rem;
      position: relative;
    }

    .success-icon {
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, var(--success-color), #20c997);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 4rem;
      color: white;
      box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
      animation: successPulse 2s ease-in-out infinite;
    }

    @keyframes successPulse {
      0%, 100% {
        transform: scale(1);
        box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
      }
      50% {
        transform: scale(1.05);
        box-shadow: 0 15px 40px rgba(40, 167, 69, 0.4);
      }
    }

    .success-checkmark {
      position: relative;
    }

    .success-checkmark::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 60px;
      height: 60px;
      border: 3px solid white;
      border-radius: 50%;
      animation: checkmarkCircle 1s ease-in-out;
    }

    @keyframes checkmarkCircle {
      0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
      }
      50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 1;
      }
      100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
      }
    }

    .success-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--success-color);
      margin-bottom: 1rem;
      animation: slideInUp 0.8s ease-out;
    }

    .success-message {
      font-size: 1.2rem;
      color: #666;
      margin-bottom: 2rem;
      line-height: 1.6;
      animation: slideInUp 0.8s ease-out 0.2s both;
    }

    .order-details {
      background: #f8f9fa;
      border-radius: 15px;
      padding: 2rem;
      margin: 2rem 0;
      animation: slideInUp 0.8s ease-out 0.4s both;
    }

    .order-detail-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem 0;
      border-bottom: 1px solid #e9ecef;
    }

    .order-detail-item:last-child {
      border-bottom: none;
    }

    .order-detail-label {
      font-weight: 600;
      color: #495057;
    }

    .order-detail-value {
      font-weight: 700;
      color: var(--primary-color);
    }

    .order-number {
      font-size: 1.1rem;
      color: var(--success-color);
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin-top: 2rem;
      animation: slideInUp 0.8s ease-out 0.6s both;
    }

    .btn-primary-custom {
      background: linear-gradient(135deg, var(--primary-color), #7c4dff);
      border: none;
      border-radius: 50px;
      padding: 1rem 2rem;
      font-weight: 600;
      font-size: 1.1rem;
      color: white;
      text-decoration: none;
      transition: all 0.3s ease;
      box-shadow: 0 5px 15px rgba(98, 0, 234, 0.3);
    }

    .btn-primary-custom:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(98, 0, 234, 0.4);
      color: white;
    }

    .btn-outline-custom {
      background: transparent;
      border: 2px solid var(--primary-color);
      border-radius: 50px;
      padding: 1rem 2rem;
      font-weight: 600;
      font-size: 1.1rem;
      color: var(--primary-color);
      text-decoration: none;
      transition: all 0.3s ease;
    }

    .btn-outline-custom:hover {
      background: var(--primary-color);
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(98, 0, 234, 0.3);
    }

    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .floating-elements {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      overflow: hidden;
    }

    .floating-element {
      position: absolute;
      opacity: 0.1;
      animation: float 6s ease-in-out infinite;
    }

    .floating-element:nth-child(1) {
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    .floating-element:nth-child(2) {
      top: 20%;
      right: 10%;
      animation-delay: 2s;
    }

    .floating-element:nth-child(3) {
      bottom: 20%;
      left: 15%;
      animation-delay: 4s;
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-20px);
      }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .success-card {
        padding: 2rem 1.5rem;
        margin: 1rem;
      }

      .success-title {
        font-size: 2rem;
      }

      .success-message {
        font-size: 1rem;
      }

      .action-buttons {
        flex-direction: column;
        align-items: center;
      }

      .btn-primary-custom,
      .btn-outline-custom {
        width: 100%;
        max-width: 300px;
      }

      .success-illustration {
        width: 150px;
        height: 150px;
      }

      .success-icon {
        font-size: 3rem;
      }
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
      <a class="navbar-brand" href="/">LUXE SCENTS</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <a class="nav-link" href="/">Home</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/shop">Shop</a>
          </li>
        </ul>
        <div class="navbar-icons">
          <a href="/wishlist" class="text-white me-3">
            <i class="fas fa-heart"></i>
            <span class="badge bg-danger" id="wishlistCount">0</span>
          </a>
          <a href="/cart" class="text-white me-3">
            <i class="fas fa-shopping-cart"></i>
            <span class="badge bg-danger" id="cartCount">0</span>
          </a>
          <div class="dropdown">
            <a href="#" class="dropdown-toggle text-white" role="button" data-bs-toggle="dropdown">
              <i class="fas fa-user-circle"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li><a class="dropdown-item" href="/profile">My Profile</a></li>
              <li><a class="dropdown-item" href="/addresses">My Addresses</a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="/logout">Logout</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <div class="success-container">
    <div class="success-card">
      <!-- Floating Background Elements -->
      <div class="floating-elements">
        <i class="fas fa-star floating-element"></i>
        <i class="fas fa-heart floating-element"></i>
        <i class="fas fa-gift floating-element"></i>
      </div>

      <!-- Success Illustration -->
      <div class="success-illustration">
        <div class="success-icon">
          <i class="fas fa-check success-checkmark"></i>
        </div>
      </div>

      <!-- Success Message -->
      <h1 class="success-title">Order Placed Successfully!</h1>
      <p class="success-message">
        Thank you for choosing Luxe Scents! Your order has been confirmed and is being processed. 
        We'll send you an email confirmation shortly with all the details.
      </p>

      <!-- Order Details -->
      <div class="order-details">
        <div class="order-detail-item">
          <span class="order-detail-label">Order Number:</span>
          <span class="order-detail-value order-number"><%= order.orderNumber %></span>
        </div>
        <div class="order-detail-item">
          <span class="order-detail-label">Total Amount:</span>
          <span class="order-detail-value">₹<%= order.totalAmount.toFixed(2) %></span>
        </div>
        <div class="order-detail-item">
          <span class="order-detail-label">Payment Method:</span>
          <span class="order-detail-value"><%= order.paymentMethod %></span>
        </div>
        <div class="order-detail-item">
          <span class="order-detail-label">Expected Delivery:</span>
          <span class="order-detail-value"><%= new Date(order.expectedDelivery).toLocaleDateString('en-IN', { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          }) %></span>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <a href="/orders/<%= order._id %>" class="btn-primary-custom">
          <i class="fas fa-receipt me-2"></i>Go to Order Details
        </a>
        <a href="/" class="btn-outline-custom">
          <i class="fas fa-shopping-bag me-2"></i>Continue Shopping
        </a>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Update cart and wishlist counts
    function updateCounts() {
      fetch('/api/cart/count')
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            const cartBadge = document.getElementById('cartCount');
            if (cartBadge) {
              cartBadge.textContent = data.cartCount;
            }
          }
        })
        .catch(error => {
          console.error('Error updating cart count:', error);
        });

      fetch('/api/wishlist/count')
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            const wishlistBadge = document.getElementById('wishlistCount');
            if (wishlistBadge) {
              wishlistBadge.textContent = data.wishlistCount;
            }
          }
        })
        .catch(error => {
          console.error('Error updating wishlist count:', error);
        });
    }

    // Initialize counts on page load
    document.addEventListener('DOMContentLoaded', function() {
      updateCounts();
    });
  </script>
</body>
</html>
