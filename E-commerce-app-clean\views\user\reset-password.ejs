<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Reset Password | Luxe Scents</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .reset-container {
      max-width: 500px;
      width: 100%;
      margin: 2rem;
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    .reset-header {
      background: linear-gradient(135deg, #6200ea, #bb86fc);
      color: white;
      padding: 2rem;
      text-align: center;
    }
    .reset-header h2 {
      margin-bottom: 0.5rem;
      font-weight: 600;
    }
    .reset-header p {
      margin-bottom: 0;
      opacity: 0.9;
    }
    .reset-body {
      padding: 2rem;
    }
    .form-control {
      border-radius: 10px;
      border: 2px solid #e9ecef;
      padding: 0.75rem 1rem;
      transition: all 0.3s;
      font-size: 1rem;
    }
    .form-control:focus {
      border-color: #6200ea;
      box-shadow: 0 0 0 0.2rem rgba(98, 0, 234, 0.25);
    }
    .btn-primary {
      background-color: #6200ea;
      border-color: #6200ea;
      border-radius: 25px;
      padding: 0.75rem 2rem;
      font-weight: 500;
      transition: all 0.3s;
      width: 100%;
      font-size: 1rem;
    }
    .btn-primary:hover {
      background-color: #5300d1;
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(98, 0, 234, 0.3);
    }
    .btn-outline-secondary {
      border-radius: 25px;
      padding: 0.75rem 2rem;
      font-weight: 500;
      transition: all 0.3s;
      width: 100%;
      font-size: 1rem;
    }
    .btn-outline-secondary:hover {
      transform: translateY(-2px);
    }
    .alert {
      border-radius: 10px;
      border: none;
      margin-bottom: 1.5rem;
    }
    .alert-success {
      background-color: #d4edda;
      color: #155724;
    }
    .alert-danger {
      background-color: #f8d7da;
      color: #721c24;
    }
    .password-requirements {
      background-color: #f8f9fa;
      padding: 1rem;
      border-radius: 10px;
      margin-bottom: 1rem;
      font-size: 0.9rem;
    }
    .password-requirements ul {
      margin-bottom: 0;
      padding-left: 1.2rem;
    }
    .password-requirements li {
      margin-bottom: 0.3rem;
    }
    .input-group {
      position: relative;
    }
    .password-toggle {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      color: #6c757d;
      cursor: pointer;
      z-index: 10;
    }
    .password-toggle:hover {
      color: #6200ea;
    }
    .back-link {
      text-align: center;
      margin-top: 1.5rem;
    }
    .back-link a {
      color: #6200ea;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s;
    }
    .back-link a:hover {
      color: #5300d1;
    }
    @media (max-width: 768px) {
      .reset-container {
        margin: 1rem;
      }
      .reset-header {
        padding: 1.5rem;
      }
      .reset-body {
        padding: 1.5rem;
      }
    }
  </style>
</head>
<body>
  <div class="reset-container">
    <!-- Header -->
    <div class="reset-header">
      <h2><i class="fas fa-lock me-2"></i>Reset Password</h2>
      <p>Create a new password for your account</p>
    </div>

    <!-- Body -->
    <div class="reset-body">
      <!-- Success/Error Messages -->
      <% if (message) { %>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
          <i class="fas fa-check-circle me-2"></i><%= message %>
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      <% } %>
      <% if (error) { %>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
          <i class="fas fa-exclamation-circle me-2"></i><%= error %>
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      <% } %>

      <!-- Password Requirements -->
      <div class="password-requirements">
        <h6><i class="fas fa-info-circle me-2"></i>Password Requirements</h6>
        <ul>
          <li>At least 8 characters long</li>
          <li>Mix of uppercase and lowercase letters recommended</li>
          <li>Include numbers and special characters for better security</li>
        </ul>
      </div>

      <!-- Reset Password Form -->
      <form action="/reset-password/<%= token %>" method="POST" id="resetPasswordForm">
        <!-- New Password -->
        <div class="mb-3">
          <label for="password" class="form-label">New Password</label>
          <div class="input-group">
            <input type="password" class="form-control" id="password" name="password" required minlength="8" placeholder="Enter new password">
            <button type="button" class="password-toggle" onclick="togglePassword('password')">
              <i class="fas fa-eye" id="passwordIcon"></i>
            </button>
          </div>
        </div>

        <!-- Confirm New Password -->
        <div class="mb-3">
          <label for="confirmPassword" class="form-label">Confirm New Password</label>
          <div class="input-group">
            <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required minlength="8" placeholder="Confirm new password">
            <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
              <i class="fas fa-eye" id="confirmPasswordIcon"></i>
            </button>
          </div>
          <div class="form-text" id="passwordMatch"></div>
        </div>

        <div class="d-grid gap-2">
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-2"></i>Reset Password
          </button>
          <a href="/login" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Login
          </a>
        </div>
      </form>

      <!-- Additional Links -->
      <div class="back-link">
        <p class="text-muted mb-2">Remember your password?</p>
        <a href="/login">
          <i class="fas fa-sign-in-alt me-1"></i>Sign In
        </a>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    function togglePassword(fieldId) {
      const field = document.getElementById(fieldId);
      const icon = document.getElementById(fieldId + 'Icon');

      if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
      } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
      }
    }

    // Password confirmation validation
    document.getElementById('confirmPassword').addEventListener('input', function() {
      const password = document.getElementById('password').value;
      const confirmPassword = this.value;
      const matchDiv = document.getElementById('passwordMatch');

      if (confirmPassword === '') {
        matchDiv.textContent = '';
        matchDiv.className = 'form-text';
      } else if (password === confirmPassword) {
        matchDiv.textContent = '✓ Passwords match';
        matchDiv.className = 'form-text text-success';
      } else {
        matchDiv.textContent = '✗ Passwords do not match';
        matchDiv.className = 'form-text text-danger';
      }
    });

    // Enhanced form validation with comprehensive password rules
    document.getElementById('resetPasswordForm').addEventListener('submit', function(e) {
      const password = document.getElementById('password').value;
      const confirmPassword = document.getElementById('confirmPassword').value;

      // Password validation
      if (!password) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Password Required',
          text: 'Please enter a new password',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      if (password.length < 8) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Password Too Short',
          text: 'Password must be at least 8 characters long',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      if (password.length > 128) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Password Too Long',
          text: 'Password must be less than 128 characters long',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Check for spaces in password
      if (/\s/.test(password)) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Invalid Password Format',
          text: 'Password cannot contain spaces',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Password strength validation
      if (!/(?=.*[a-z])/.test(password)) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Weak Password',
          text: 'Password must contain at least one lowercase letter',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      if (!/(?=.*[A-Z])/.test(password)) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Weak Password',
          text: 'Password must contain at least one uppercase letter',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      if (!/(?=.*\d)/.test(password)) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Weak Password',
          text: 'Password must contain at least one number',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      if (!/(?=.*[@$!%*?&_-])/.test(password)) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Weak Password',
          text: 'Password must contain at least one special character (@$!%*?&_-)',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Check for consecutive identical characters
      if (/(.)\1{2,}/.test(password)) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Weak Password',
          text: 'Password cannot contain more than 2 consecutive identical characters',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Confirm password validation
      if (!confirmPassword) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Confirm Password Required',
          text: 'Please confirm your new password',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      if (password !== confirmPassword) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Password Mismatch',
          text: 'Passwords do not match. Please check and try again.',
          confirmButtonColor: '#6200ea'
        });
        return;
      }
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
      const alerts = document.querySelectorAll('.alert');
      alerts.forEach(function(alert) {
        const bsAlert = new bootstrap.Alert(alert);
        bsAlert.close();
      });
    }, 5000);
  </script>
</body>
</html>
