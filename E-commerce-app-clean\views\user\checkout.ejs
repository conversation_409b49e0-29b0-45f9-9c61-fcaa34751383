<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Checkout - Luxe Scents</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    :root {
      --primary-color: #6200ea;
      --secondary-color: #f8f9fa;
      --success-color: #28a745;
      --danger-color: #dc3545;
      --warning-color: #ffc107;
      --info-color: #17a2b8;
      --dark-color: #343a40;
      --light-color: #f8f9fa;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: var(--secondary-color);
      color: var(--dark-color);
    }

    .navbar {
      background-color: #000000;
      padding: 1rem 2rem;
      border-bottom: 1px solid #090909;
    }
    .navbar-brand {
      font-weight: 700;
      font-size: 1.8rem;
      color: #ffffff !important;
      letter-spacing: 1px;
    }
    .navbar-brand:hover {
      color: #bb86fc !important;
    }
    .nav-link {
      color: #e0e0e0 !important;
      font-weight: 500;
      margin: 0 1rem;
      transition: color 0.3s;
    }
    .nav-link:hover {
      color: #bb86fc !important;
    }
    .navbar-icons {
      display: flex;
      align-items: center;
      gap: 1.5rem;
    }
    .navbar-icons a {
      color: #e0e0e0;
      font-size: 1.2rem;
      position: relative;
      transition: color 0.3s;
    }
    .navbar-icons a:hover {
      color: #bb86fc;
    }
    .badge {
      position: absolute;
      top: -8px;
      right: -8px;
      background-color: #6200ea;
      color: #ffffff;
      font-size: 0.7rem;
      border-radius: 50%;
      padding: 2px 6px;
    }
    .dropdown-menu {
      background-color: #2a2a2a;
      border: 1px solid #444;
    }
    .dropdown-item {
      color: #e0e0e0 !important;
      transition: background-color 0.3s, color 0.3s;
    }
    .dropdown-item:hover {
      background-color: #6200ea;
      color: #ffffff !important;
    }

    .checkout-container {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .checkout-header {
      background: white;
      padding: 1.5rem;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      margin-bottom: 2rem;
      text-align: center;
    }

    .checkout-header h1 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
      font-weight: 700;
    }

    .checkout-steps {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 1rem;
    }

    .step {
      display: flex;
      align-items: center;
      color: var(--primary-color);
      font-weight: 600;
    }

    .step-number {
      background: var(--primary-color);
      color: white;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 0.5rem;
      font-size: 0.9rem;
    }

    .step-divider {
      width: 50px;
      height: 2px;
      background: var(--primary-color);
      margin: 0 1rem;
    }

    .checkout-content {
      display: grid;
      grid-template-columns: 1fr 400px;
      gap: 2rem;
    }

    .main-content {
      background: white;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .sidebar {
      background: white;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      height: fit-content;
      position: sticky;
      top: 2rem;
    }

    .section-header {
      background: var(--primary-color);
      color: white;
      padding: 1rem 1.5rem;
      font-weight: 600;
      font-size: 1.1rem;
    }

    .section-content {
      padding: 1.5rem;
    }

    /* Address Section */
    .address-card {
      border: 2px solid #e9ecef;
      border-radius: 8px;
      padding: 1rem;
      margin-bottom: 1rem;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
    }

    .address-card:hover {
      border-color: var(--primary-color);
      box-shadow: 0 2px 8px rgba(98, 0, 234, 0.1);
    }

    .address-card.selected {
      border-color: var(--primary-color);
      background: rgba(98, 0, 234, 0.05);
    }

    .address-card .default-badge {
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
      background: var(--success-color);
      color: white;
      padding: 0.2rem 0.5rem;
      border-radius: 12px;
      font-size: 0.7rem;
      font-weight: 600;
    }

    .address-title {
      font-weight: 600;
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    .address-details {
      color: #666;
      font-size: 0.9rem;
      line-height: 1.4;
    }

    .address-actions {
      margin-top: 0.5rem;
      display: flex;
      gap: 0.5rem;
    }

    .btn-address {
      padding: 0.3rem 0.8rem;
      font-size: 0.8rem;
      border-radius: 5px;
    }

    /* Product Section */
    .product-item {
      display: flex;
      align-items: center;
      padding: 1rem 0;
      border-bottom: 1px solid #e9ecef;
    }

    .product-item:last-child {
      border-bottom: none;
    }

    .product-image {
      width: 80px;
      height: 80px;
      border-radius: 8px;
      object-fit: cover;
      margin-right: 1rem;
    }

    .product-details {
      flex: 1;
    }

    .product-name {
      font-weight: 600;
      color: var(--dark-color);
      margin-bottom: 0.3rem;
    }

    .product-category {
      color: #666;
      font-size: 0.9rem;
      margin-bottom: 0.3rem;
    }

    .product-pricing {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.3rem;
    }

    .current-price {
      font-weight: 600;
      color: var(--success-color);
    }

    .original-price {
      text-decoration: line-through;
      color: #999;
      font-size: 0.9rem;
    }

    .discount-badge {
      background: var(--danger-color);
      color: white;
      padding: 0.1rem 0.4rem;
      border-radius: 4px;
      font-size: 0.7rem;
    }

    .product-quantity {
      color: #666;
      font-size: 0.9rem;
    }

    /* Order Summary */
    .summary-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5rem 0;
      border-bottom: 1px solid #e9ecef;
    }

    .summary-row:last-child {
      border-bottom: none;
      font-weight: 600;
      font-size: 1.1rem;
      color: var(--primary-color);
      padding-top: 1rem;
      border-top: 2px solid var(--primary-color);
    }

    .summary-label {
      color: #666;
    }

    .summary-value {
      font-weight: 600;
    }

    .savings-text {
      color: var(--success-color);
      font-size: 0.9rem;
    }

    .tax-info {
      font-size: 0.8rem;
      color: #666;
    }

    /* Coupon Section */
    .coupon-section {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 1rem;
      margin-bottom: 1rem;
    }

    .coupon-input-group {
      display: flex;
      gap: 0.5rem;
    }

    .coupon-input {
      flex: 1;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 0.5rem;
    }

    .coupon-btn {
      background: var(--primary-color);
      color: white;
      border: none;
      border-radius: 5px;
      padding: 0.5rem 1rem;
      cursor: pointer;
      transition: background 0.3s ease;
    }

    .coupon-btn:hover {
      background: #5200d1;
    }

    .applied-coupon {
      background: rgba(40, 167, 69, 0.1);
      border: 1px solid var(--success-color);
      border-radius: 5px;
      padding: 0.5rem;
      margin-top: 0.5rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .coupon-code {
      font-weight: 600;
      color: var(--success-color);
    }

    .remove-coupon {
      background: none;
      border: none;
      color: var(--danger-color);
      cursor: pointer;
      font-size: 0.9rem;
    }

    /* Payment Method Section */
    .payment-section {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 1rem;
    }

    .payment-options {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .payment-option {
      border: 2px solid #e9ecef;
      border-radius: 8px;
      padding: 1rem;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .payment-option:hover:not(.disabled) {
      border-color: var(--primary-color);
      background: rgba(98, 0, 234, 0.05);
    }

    .payment-option.disabled {
      opacity: 0.6;
      cursor: not-allowed;
      background: #f8f9fa;
    }

    .payment-option input[type="radio"] {
      display: none;
    }

    .payment-option input[type="radio"]:checked + .payment-label {
      color: var(--primary-color);
    }

    .payment-option input[type="radio"]:checked + .payment-label::before {
      background: var(--primary-color);
      border-color: var(--primary-color);
    }

    .payment-label {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin: 0;
      position: relative;
      padding-left: 2rem;
    }

    .payment-label::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 18px;
      height: 18px;
      border: 2px solid #ddd;
      border-radius: 50%;
      background: white;
      transition: all 0.3s ease;
    }

    .payment-label::after {
      content: '';
      position: absolute;
      left: 6px;
      top: 50%;
      transform: translateY(-50%);
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: white;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .payment-option input[type="radio"]:checked + .payment-label::after {
      opacity: 1;
    }

    .payment-desc {
      font-size: 0.8rem;
      color: #666;
      margin-top: 0.2rem;
    }

    /* Place Order Button */
    .place-order-btn {
      width: 100%;
      background: var(--primary-color);
      color: white;
      border: none;
      border-radius: 8px;
      padding: 1rem;
      font-size: 1.1rem;
      font-weight: 600;
      cursor: pointer;
      transition: background 0.3s ease;
      margin-top: 1rem;
    }

    .place-order-btn:hover {
      background: #5200d1;
    }

    .place-order-btn:disabled {
      background: #ccc;
      cursor: not-allowed;
    }

    /* Add Address Modal */
    .modal-content {
      border-radius: 10px;
    }

    .modal-header {
      background: var(--primary-color);
      color: white;
      border-radius: 10px 10px 0 0;
    }

    /* Footer Styles */
    footer {
      background-color: #000000;
      color: #e0e0e0;
      padding: 3rem 2rem;
      margin-top: 2rem;
      border-top: 1px solid #333;
    }
    footer .container {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      gap: 2rem;
    }
    footer h5 {
      color: #ffffff;
      margin-bottom: 1rem;
      font-size: 1.2rem;
    }
    footer ul {
      list-style: none;
      padding: 0;
    }
    footer ul li {
      margin-bottom: 0.5rem;
    }
    footer ul li a {
      color: #b0b0b0;
      text-decoration: none;
      transition: color 0.3s;
    }
    footer ul li a:hover {
      color: #bb86fc;
    }
    footer .social-icons a {
      color: #e0e0e0;
      font-size: 1.5rem;
      margin-right: 1rem;
      transition: color 0.3s;
    }
    footer .social-icons a:hover {
      color: #bb86fc;
    }
    footer .footer-bottom {
      text-align: center;
      margin-top: 2rem;
      padding-top: 1rem;
      border-top: 1px solid #333;
      color: #b0b0b0;
      font-size: 0.9rem;
    }
    .footer-section {
      flex: 1;
      min-width: 250px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .checkout-content {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .sidebar {
        position: static;
      }

      .checkout-steps {
        flex-direction: column;
        gap: 0.5rem;
      }

      .step-divider {
        width: 2px;
        height: 30px;
        margin: 0.5rem 0;
      }

      footer .container {
        flex-direction: column;
        text-align: center;
      }
      footer .social-icons {
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
      <a class="navbar-brand" href="/">LUXE SCENTS</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <a class="nav-link" href="/">Home</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/shop">Shop</a>
          </li>
          <li class="nav-item">
            <a class="nav-link active" href="/checkout">Checkout</a>
          </li>
        </ul>
        <div class="navbar-icons">
          <a href="/wishlist" class="text-white me-3">
            <i class="fas fa-heart"></i>
            <span class="badge bg-danger" id="wishlistCount">0</span>
          </a>
          <a href="/cart" class="text-white me-3">
            <i class="fas fa-shopping-cart"></i>
            <span class="badge bg-danger" id="cartCount">0</span>
          </a>
          <div class="dropdown">
            <a href="#" class="dropdown-toggle text-white" role="button" data-bs-toggle="dropdown">
              <i class="fas fa-user-circle"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li><a class="dropdown-item" href="/profile">My Profile</a></li>
              <li><a class="dropdown-item" href="/addresses">My Addresses</a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="/logout">Logout</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <div class="checkout-container">
    <!-- Checkout Header -->
    <div class="checkout-header">
      <h1><i class="fas fa-shopping-cart me-2"></i>Checkout</h1>
      <p class="text-muted mb-0">Review your order and complete your purchase</p>

      <div class="checkout-steps">
        <div class="step">
          <div class="step-number">1</div>
          <span>Address</span>
        </div>
        <div class="step-divider"></div>
        <div class="step">
          <div class="step-number">2</div>
          <span>Review</span>
        </div>
        <div class="step-divider"></div>
        <div class="step">
          <div class="step-number">3</div>
          <span>Payment</span>
        </div>
      </div>
    </div>

    <div class="checkout-content">
      <!-- Main Content -->
      <div class="main-content">
        <!-- Address Selection -->
        <div class="section-header">
          <i class="fas fa-map-marker-alt me-2"></i>Delivery Address
        </div>
        <div class="section-content">
          <% if (addresses && addresses.length > 0) { %>
            <div id="addressList">
              <% addresses.forEach((address, index) => { %>
                <div class="address-card <%= address.isDefault ? 'selected' : '' %>"
                     data-address-id="<%= address._id %>"
                     onclick="selectAddress('<%= address._id %>')">
                  <% if (address.isDefault) { %>
                    <div class="default-badge">Default</div>
                  <% } %>

                  <div class="address-title">
                    <i class="fas fa-<%= address.addressType === 'Home' ? 'home' : address.addressType === 'Work' ? 'building' : 'map-marker-alt' %> me-2"></i>
                    <%= address.title %>
                  </div>

                  <div class="address-details">
                    <strong><%= address.fullName %></strong><br>
                    <%= address.getShortAddress() %><br>
                    <i class="fas fa-phone me-1"></i><%= address.phone %>
                  </div>

                  <div class="address-actions">
                    <button class="btn btn-outline-primary btn-address" onclick="editAddress('<%= address._id %>')">
                      <i class="fas fa-edit me-1"></i>Edit
                    </button>
                    <% if (!address.isDefault) { %>
                      <button class="btn btn-outline-success btn-address" onclick="setDefaultAddress('<%= address._id %>')">
                        <i class="fas fa-star me-1"></i>Set Default
                      </button>
                    <% } %>
                  </div>
                </div>
              <% }) %>
            </div>
          <% } else { %>
            <div class="text-center py-4">
              <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
              <h5>No addresses found</h5>
              <p class="text-muted">Please add a delivery address to continue</p>
            </div>
          <% } %>

          <button class="btn btn-outline-primary mt-3" onclick="showAddAddressModal()">
            <i class="fas fa-plus me-2"></i>Add New Address
          </button>
        </div>

        <!-- Order Items -->
        <div class="section-header">
          <i class="fas fa-box me-2"></i>Order Items (<%= cart.totalItems %> items)
        </div>
        <div class="section-content">
          <% cart.items.forEach(item => { %>
            <div class="product-item">
              <% if (item.product.productImage && item.product.productImage.length > 0) { %>
                <img src="/uploads/product-images/<%= item.product.productImage[0] %>"
                     alt="<%= item.product.productName %>"
                     class="product-image">
              <% } else { %>
                <img src="/images/default-product.jpg"
                     alt="No image"
                     class="product-image">
              <% } %>

              <div class="product-details">
                <div class="product-name"><%= item.product.productName %></div>
                <div class="product-category"><%= item.product.category.name %></div>

                <div class="product-pricing">
                  <% if (item.product.salePrice && item.product.salePrice < item.product.price) { %>
                    <span class="current-price">₹<%= item.product.salePrice.toFixed(2) %></span>
                    <span class="original-price">₹<%= item.product.price.toFixed(2) %></span>
                    <% if (item.product.discount > 0) { %>
                      <span class="discount-badge">-<%= item.product.discount %>%</span>
                    <% } %>
                  <% } else { %>
                    <span class="current-price">₹<%= item.product.price.toFixed(2) %></span>
                  <% } %>
                </div>

                <div class="product-quantity">
                  <i class="fas fa-cube me-1"></i>Quantity: <%= item.quantity %>
                </div>
              </div>
            </div>
          <% }) %>
        </div>
      </div>

      <!-- Sidebar - Order Summary -->
      <div class="sidebar">
        <div class="section-header">
          <i class="fas fa-receipt me-2"></i>Order Summary
        </div>
        <div class="section-content">
          <!-- Coupon Section -->
          <div class="coupon-section">
            <h6 class="mb-2"><i class="fas fa-tag me-2"></i>Apply Coupon</h6>
            <div id="couponInputSection">
              <div class="coupon-input-group">
                <input type="text" id="couponCode" class="coupon-input" placeholder="Enter coupon code" maxlength="20">
                <button class="coupon-btn" onclick="applyCoupon()">Apply</button>
              </div>
            </div>
            <div id="appliedCouponSection" style="display: none;">
              <div class="applied-coupon">
                <div>
                  <i class="fas fa-tag me-2"></i>
                  <span class="coupon-code" id="appliedCouponCode"></span>
                </div>
                <button class="remove-coupon" onclick="removeCoupon()">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Order Summary -->
          <div id="orderSummary">
            <div class="summary-row">
              <span class="summary-label">Subtotal (<%= cart.totalItems %> items)</span>
              <span class="summary-value">₹<%= orderSummary.subtotal.toFixed(2) %></span>
            </div>

            <% if (orderSummary.savings > 0) { %>
              <div class="summary-row">
                <span class="summary-label savings-text">Product Savings</span>
                <span class="summary-value savings-text">-₹<%= orderSummary.savings.toFixed(2) %></span>
              </div>
            <% } %>

            <div class="summary-row" id="couponDiscountRow" style="display: none;">
              <span class="summary-label savings-text">Coupon Discount</span>
              <span class="summary-value savings-text" id="couponDiscountValue">-₹0.00</span>
            </div>

            <div class="summary-row">
              <span class="summary-label">
                Tax (GST <%= taxConfig.GST_RATE %>%)
                <div class="tax-info">Inclusive of all taxes</div>
              </span>
              <span class="summary-value">₹<%= orderSummary.tax.toFixed(2) %></span>
            </div>

            <div class="summary-row">
              <span class="summary-label">
                Shipping Charges
                <% if (orderSummary.shipping === 0) { %>
                  <div class="tax-info savings-text">Free shipping applied!</div>
                <% } else { %>
                  <div class="tax-info">Free shipping on orders above ₹<%= orderSummary.freeShippingThreshold %></div>
                <% } %>
              </span>
              <span class="summary-value">
                <% if (orderSummary.shipping === 0) { %>
                  <span class="savings-text">FREE</span>
                <% } else { %>
                  ₹<%= orderSummary.shipping.toFixed(2) %>
                <% } %>
              </span>
            </div>

            <div class="summary-row">
              <span class="summary-label">Total Amount</span>
              <span class="summary-value" id="finalTotal">₹<%= orderSummary.total.toFixed(2) %></span>
            </div>
          </div>

          <!-- Payment Method Selection -->
          <div class="payment-section mb-3">
            <h6 class="mb-2"><i class="fas fa-credit-card me-2"></i>Payment Method</h6>
            <div class="payment-options">
              <div class="payment-option">
                <input type="radio" id="cod" name="paymentMethod" value="COD" checked>
                <label for="cod" class="payment-label">
                  <i class="fas fa-money-bill-wave me-2"></i>
                  <div>
                    <strong>Cash on Delivery</strong>
                    <div class="payment-desc">Pay when your order is delivered</div>
                  </div>
                </label>
              </div>
              <div class="payment-option disabled">
                <input type="radio" id="online" name="paymentMethod" value="Online" disabled>
                <label for="online" class="payment-label">
                  <i class="fas fa-credit-card me-2"></i>
                  <div>
                    <strong>Online Payment</strong>
                    <div class="payment-desc">Coming Soon</div>
                  </div>
                </label>
              </div>
            </div>
          </div>

          <!-- Place Order Button -->
          <button class="place-order-btn" id="placeOrderBtn" onclick="placeOrder()"
                  <%= (!addresses || addresses.length === 0) ? 'disabled' : '' %>>
            <% if (!addresses || addresses.length === 0) { %>
              <i class="fas fa-exclamation-triangle me-2"></i>Add Address to Continue
            <% } else { %>
              <i class="fas fa-shopping-bag me-2"></i>Place Order
            <% } %>
          </button>

          <div class="text-center mt-3">
            <small class="text-muted">
              <i class="fas fa-shield-alt me-1"></i>
              Your payment information is secure and encrypted
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Add Address Modal -->
  <div class="modal fade" id="addAddressModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="fas fa-plus me-2"></i>Add New Address
          </h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <form id="addAddressForm">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="addressTitle" class="form-label">Address Title *</label>
                <input type="text" class="form-control" id="addressTitle" name="title" required maxlength="50">
                <div class="form-text">e.g., Home, Office, etc.</div>
              </div>
              <div class="col-md-6 mb-3">
                <label for="addressType" class="form-label">Address Type</label>
                <select class="form-select" id="addressType" name="addressType">
                  <option value="Home">Home</option>
                  <option value="Work">Work</option>
                  <option value="Other">Other</option>
                </select>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="fullName" class="form-label">Full Name *</label>
                <input type="text" class="form-control" id="fullName" name="fullName" required maxlength="100">
              </div>
              <div class="col-md-6 mb-3">
                <label for="phone" class="form-label">Phone Number *</label>
                <input type="tel" class="form-control" id="phone" name="phone" required pattern="^\+91\d{10}$" placeholder="+91XXXXXXXXXX">
              </div>
            </div>

            <div class="mb-3">
              <label for="street" class="form-label">Street Address *</label>
              <textarea class="form-control" id="street" name="street" rows="2" required maxlength="200"></textarea>
            </div>

            <div class="mb-3">
              <label for="landmark" class="form-label">Landmark (Optional)</label>
              <input type="text" class="form-control" id="landmark" name="landmark" maxlength="100">
            </div>

            <div class="row">
              <div class="col-md-4 mb-3">
                <label for="city" class="form-label">City *</label>
                <input type="text" class="form-control" id="city" name="city" required maxlength="50">
              </div>
              <div class="col-md-4 mb-3">
                <label for="state" class="form-label">State *</label>
                <input type="text" class="form-control" id="state" name="state" required maxlength="50">
              </div>
              <div class="col-md-4 mb-3">
                <label for="zipCode" class="form-label">ZIP Code *</label>
                <input type="text" class="form-control" id="zipCode" name="zipCode" required pattern="^\d{6}$" maxlength="6">
              </div>
            </div>

            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="setAsDefault" name="isDefault">
                <label class="form-check-label" for="setAsDefault">
                  Set as default address
                </label>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary" onclick="saveAddress()">
            <i class="fas fa-save me-2"></i>Save Address
          </button>
        </div>
      </div>
    </div>
  </div>

  <footer>
    <div class="container">
      <div class="footer-section">
        <h5>About Luxe Scents</h5>
        <p style="color: #b0b0b0; max-width: 300px;">
          Discover the finest luxury fragrances crafted for every occasion. Elevate your senses with Luxe Scents.
        </p>
      </div>
      <div class="footer-section">
        <h5>Quick Links</h5>
        <ul>
          <li><a href="/">Home</a></li>
          <li><a href="/shop">Shop</a></li>
          <li><a href="#">Contact Us</a></li>
          <li><a href="#">FAQs</a></li>
        </ul>
      </div>
      <div class="footer-section">
        <h5>Contact Us</h5>
        <ul>
          <li>Email: <EMAIL></li>
          <li>Phone: +****************</li>
          <li>Address: 123 Fragrance Lane, Scent City</li>
        </ul>
      </div>
      <div class="footer-section">
        <h5>Follow Us</h5>
        <div class="social-icons">
          <a href="#"><i class="fab fa-facebook-f"></i></a>
          <a href="#"><i class="fab fa-instagram"></i></a>
          <a href="#"><i class="fab fa-twitter"></i></a>
          <a href="#"><i class="fab fa-pinterest"></i></a>
        </div>
      </div>
    </div>
    <div class="footer-bottom">
      <p>© 2025 Luxe Scents. All rights reserved.</p>
    </div>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    let selectedAddressId = '<%= defaultAddress ? defaultAddress._id : "" %>';
    let appliedCouponCode = null;
    let currentOrderSummary = <%- JSON.stringify(orderSummary) %>;

    // Address selection
    function selectAddress(addressId) {
      selectedAddressId = addressId;

      // Update UI
      document.querySelectorAll('.address-card').forEach(card => {
        card.classList.remove('selected');
      });

      document.querySelector(`[data-address-id="${addressId}"]`).classList.add('selected');

      // Enable place order button if disabled
      const placeOrderBtn = document.getElementById('placeOrderBtn');
      if (placeOrderBtn.disabled) {
        placeOrderBtn.disabled = false;
        placeOrderBtn.innerHTML = '<i class="fas fa-credit-card me-2"></i>Place Order';
      }
    }

    // Show add address modal
    function showAddAddressModal() {
      const modal = new bootstrap.Modal(document.getElementById('addAddressModal'));
      modal.show();
    }

    // Save new address
    async function saveAddress() {
      const form = document.getElementById('addAddressForm');
      const formData = new FormData(form);

      // Convert FormData to JSON
      const addressData = {};
      formData.forEach((value, key) => {
        if (key === 'isDefault') {
          addressData[key] = true;
        } else {
          addressData[key] = value;
        }
      });

      try {
        const response = await fetch('/profile/address/add', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(addressData)
        });

        const result = await response.json();

        if (result.success) {
          Swal.fire({
            icon: 'success',
            title: 'Address Added!',
            text: result.message,
            confirmButtonColor: '#6200ea'
          }).then(() => {
            location.reload(); // Reload to show new address
          });
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: result.message,
            confirmButtonColor: '#6200ea'
          });
        }
      } catch (error) {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to save address',
          confirmButtonColor: '#6200ea'
        });
      }
    }

    // Apply coupon
    async function applyCoupon() {
      const couponCode = document.getElementById('couponCode').value.trim();

      if (!couponCode) {
        Swal.fire({
          icon: 'warning',
          title: 'Invalid Input',
          text: 'Please enter a coupon code',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      try {
        const response = await fetch('/checkout/apply-coupon', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ couponCode })
        });

        const result = await response.json();

        if (result.success) {
          appliedCouponCode = couponCode;
          updateOrderSummary(result.orderSummary);
          showAppliedCoupon(couponCode);

          Swal.fire({
            icon: 'success',
            title: 'Coupon Applied!',
            text: result.message,
            confirmButtonColor: '#6200ea',
            timer: 2000,
            timerProgressBar: true
          });
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Invalid Coupon',
            text: result.message,
            confirmButtonColor: '#6200ea'
          });
        }
      } catch (error) {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to apply coupon',
          confirmButtonColor: '#6200ea'
        });
      }
    }

    // Remove coupon
    async function removeCoupon() {
      try {
        const response = await fetch('/checkout/remove-coupon', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        const result = await response.json();

        if (result.success) {
          appliedCouponCode = null;
          updateOrderSummary(result.orderSummary);
          hideAppliedCoupon();

          Swal.fire({
            icon: 'success',
            title: 'Coupon Removed',
            text: result.message,
            confirmButtonColor: '#6200ea',
            timer: 2000,
            timerProgressBar: true
          });
        }
      } catch (error) {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to remove coupon',
          confirmButtonColor: '#6200ea'
        });
      }
    }

    // Show applied coupon
    function showAppliedCoupon(couponCode) {
      document.getElementById('couponInputSection').style.display = 'none';
      document.getElementById('appliedCouponSection').style.display = 'block';
      document.getElementById('appliedCouponCode').textContent = couponCode;
    }

    // Hide applied coupon
    function hideAppliedCoupon() {
      document.getElementById('couponInputSection').style.display = 'block';
      document.getElementById('appliedCouponSection').style.display = 'none';
      document.getElementById('couponCode').value = '';
    }

    // Update order summary
    function updateOrderSummary(orderSummary) {
      currentOrderSummary = orderSummary;

      // Update coupon discount row
      if (orderSummary.couponDiscount && orderSummary.couponDiscount > 0) {
        document.getElementById('couponDiscountRow').style.display = 'flex';
        document.getElementById('couponDiscountValue').textContent = `-₹${orderSummary.couponDiscount.toFixed(2)}`;
      } else {
        document.getElementById('couponDiscountRow').style.display = 'none';
      }

      // Update final total
      const finalTotal = orderSummary.finalTotal || orderSummary.total;
      document.getElementById('finalTotal').textContent = `₹${finalTotal.toFixed(2)}`;
    }

    // Edit address (placeholder)
    function editAddress(addressId) {
      Swal.fire({
        icon: 'info',
        title: 'Edit Address',
        text: 'Address editing functionality will be implemented in the profile section',
        confirmButtonColor: '#6200ea'
      });
    }

    // Set default address (placeholder)
    function setDefaultAddress(addressId) {
      Swal.fire({
        icon: 'info',
        title: 'Set Default',
        text: 'Set default address functionality will be implemented',
        confirmButtonColor: '#6200ea'
      });
    }

    // Place order
    async function placeOrder() {
      if (!selectedAddressId) {
        Swal.fire({
          icon: 'warning',
          title: 'Address Required',
          text: 'Please select a delivery address',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Get selected payment method
      const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked')?.value;
      if (!paymentMethod) {
        Swal.fire({
          icon: 'warning',
          title: 'Payment Method Required',
          text: 'Please select a payment method',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Disable place order button
      const placeOrderBtn = document.getElementById('placeOrderBtn');
      const originalText = placeOrderBtn.innerHTML;
      placeOrderBtn.disabled = true;
      placeOrderBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Placing Order...';

      try {
        const orderData = {
          addressId: selectedAddressId,
          paymentMethod: paymentMethod,
          couponCode: appliedCouponCode,
          orderSummary: currentOrderSummary
        };

        const response = await fetch('/checkout/place-order', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(orderData)
        });

        const result = await response.json();

        if (result.success) {
          // Redirect to order success page
          window.location.href = result.redirectUrl;
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Order Failed',
            text: result.message,
            confirmButtonColor: '#6200ea'
          });

          // Re-enable button
          placeOrderBtn.disabled = false;
          placeOrderBtn.innerHTML = originalText;
        }
      } catch (error) {
        console.error('Error placing order:', error);
        Swal.fire({
          icon: 'error',
          title: 'Order Failed',
          text: 'Failed to place order. Please try again.',
          confirmButtonColor: '#6200ea'
        });

        // Re-enable button
        placeOrderBtn.disabled = false;
        placeOrderBtn.innerHTML = originalText;
      }
    }

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
      // Auto-select default address if available
      if (selectedAddressId) {
        selectAddress(selectedAddressId);
      }
    });
  </script>
</body>
</html>