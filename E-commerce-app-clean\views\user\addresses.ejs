<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>My Addresses | Luxe Scents</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    body {
      background-color: #f8f9fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .navbar {
      background-color: #000000;
      padding: 1rem 2rem;
      border-bottom: 1px solid #090909;
    }
    .navbar-brand {
      font-weight: 700;
      font-size: 1.8rem;
      color: #ffffff !important;
      letter-spacing: 1px;
    }
    .nav-link {
      color: #e0e0e0 !important;
      font-weight: 500;
      margin: 0 1rem;
      transition: color 0.3s;
    }
    .nav-link:hover {
      color: #bb86fc !important;
    }
    .addresses-container {
      max-width: 1000px;
      margin: 2rem auto;
      padding: 0 1rem;
    }
    .page-header {
      background: linear-gradient(135deg, #6200ea, #bb86fc);
      color: white;
      padding: 2rem;
      border-radius: 15px;
      margin-bottom: 2rem;
      text-align: center;
    }
    .address-card {
      background: white;
      border-radius: 15px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      margin-bottom: 1.5rem;
      overflow: hidden;
      transition: transform 0.3s, box-shadow 0.3s;
    }
    .address-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    .address-header {
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #dee2e6;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .address-title {
      font-weight: 600;
      color: #333;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    .default-badge {
      background-color: #28a745;
      color: white;
      font-size: 0.7rem;
      padding: 0.2rem 0.5rem;
      border-radius: 10px;
      font-weight: 500;
    }
    .address-type {
      background-color: #6200ea;
      color: white;
      font-size: 0.7rem;
      padding: 0.2rem 0.5rem;
      border-radius: 10px;
      font-weight: 500;
    }
    .address-body {
      padding: 1.5rem;
    }
    .address-info {
      margin-bottom: 1rem;
    }
    .address-info h6 {
      color: #333;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }
    .address-text {
      color: #666;
      line-height: 1.6;
    }
    .address-actions {
      display: flex;
      gap: 0.5rem;
      flex-wrap: wrap;
    }
    .btn-sm {
      padding: 0.4rem 0.8rem;
      font-size: 0.85rem;
      border-radius: 20px;
      font-weight: 500;
      transition: all 0.3s;
    }
    .btn-primary {
      background-color: #6200ea;
      border-color: #6200ea;
    }
    .btn-primary:hover {
      background-color: #5300d1;
      transform: translateY(-1px);
    }
    .btn-success {
      background-color: #28a745;
      border-color: #28a745;
    }
    .btn-success:hover {
      background-color: #218838;
      transform: translateY(-1px);
    }
    .btn-danger {
      background-color: #dc3545;
      border-color: #dc3545;
    }
    .btn-danger:hover {
      background-color: #c82333;
      transform: translateY(-1px);
    }
    .add-address-btn {
      background: linear-gradient(135deg, #6200ea, #bb86fc);
      border: none;
      color: white;
      padding: 1rem 2rem;
      border-radius: 25px;
      font-weight: 600;
      font-size: 1.1rem;
      transition: all 0.3s;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }
    .add-address-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(98, 0, 234, 0.3);
      color: white;
    }
    .empty-state {
      text-align: center;
      padding: 3rem;
      color: #666;
    }
    .empty-state i {
      font-size: 4rem;
      color: #6200ea;
      margin-bottom: 1rem;
    }
    .alert {
      border-radius: 10px;
      border: none;
    }
    @media (max-width: 768px) {
      .addresses-container {
        margin: 1rem auto;
        padding: 0 0.5rem;
      }
      .address-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
      }
      .address-actions {
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
      <a class="navbar-brand" href="/">LUXE SCENTS</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <a class="nav-link" href="/">Home</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/shop">Shop</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/profile">Profile</a>
          </li>
          <li class="nav-item">
            <a class="nav-link active" href="/addresses">Addresses</a>
          </li>
        </ul>
        <div class="navbar-icons">
          <div class="dropdown">
            <a href="#" class="dropdown-toggle text-white" role="button" data-bs-toggle="dropdown">
              <i class="fas fa-user-circle"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li><a class="dropdown-item" href="/profile">My Profile</a></li>
              <li><a class="dropdown-item" href="/addresses">My Addresses</a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="confirmLogout(event)">Logout</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- Addresses Container -->
  <div class="addresses-container">
    <!-- Page Header -->
    <div class="page-header">
      <h1><i class="fas fa-map-marker-alt me-2"></i>My Addresses</h1>
      <p class="mb-0">Manage your delivery addresses</p>
    </div>

    <!-- Success/Error Messages -->
    <% if (message) { %>
      <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><%= message %>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    <% } %>
    <% if (error) { %>
      <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i><%= error %>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    <% } %>

    <!-- Add Address Button -->
    <div class="text-center mb-4">
      <a href="/addresses/add" class="add-address-btn">
        <i class="fas fa-plus"></i>
        Add New Address
      </a>
    </div>

    <!-- Addresses List -->
    <% if (addresses && addresses.length > 0) { %>
      <% addresses.forEach(address => { %>
        <div class="address-card">
          <div class="address-header">
            <div class="address-title">
              <i class="fas fa-<%= address.addressType === 'Home' ? 'home' : address.addressType === 'Work' ? 'building' : 'map-marker-alt' %>"></i>
              <%= address.title %>
              <% if (address.isDefault) { %>
                <span class="default-badge">DEFAULT</span>
              <% } %>
              <span class="address-type"><%= address.addressType %></span>
            </div>
          </div>
          <div class="address-body">
            <div class="address-info">
              <h6><%= address.fullName %></h6>
              <div class="address-text">
                <%= address.street %><% if (address.landmark) { %>, <%= address.landmark %><% } %><br>
                <%= address.city %>, <%= address.state %> - <%= address.zipCode %><br>
                <%= address.country %><br>
                <strong>Phone:</strong> <%= address.phone %>
              </div>
            </div>
            <div class="address-actions">
              <a href="/addresses/edit/<%= address._id %>" class="btn btn-primary btn-sm">
                <i class="fas fa-edit me-1"></i>Edit
              </a>
              <% if (!address.isDefault) { %>
                <button class="btn btn-success btn-sm" onclick="setDefaultAddress('<%= address._id %>')">
                  <i class="fas fa-star me-1"></i>Set Default
                </button>
              <% } %>
              <button class="btn btn-danger btn-sm" onclick="deleteAddress('<%= address._id %>')">
                <i class="fas fa-trash me-1"></i>Delete
              </button>
            </div>
          </div>
        </div>
      <% }) %>
    <% } else { %>
      <div class="empty-state">
        <i class="fas fa-map-marker-alt"></i>
        <h3>No Addresses Found</h3>
        <p>You haven't added any addresses yet. Add your first address to get started.</p>
        <a href="/addresses/add" class="add-address-btn">
          <i class="fas fa-plus"></i>
          Add Your First Address
        </a>
      </div>
    <% } %>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    function confirmLogout(event) {
      event.preventDefault();
      Swal.fire({
        title: 'Are you sure?',
        text: 'You will be logged out of your account',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#6200ea',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, logout'
      }).then((result) => {
        if (result.isConfirmed) {
          window.location.href = '/logout';
        }
      });
    }

    function setDefaultAddress(addressId) {
      Swal.fire({
        title: 'Set as Default?',
        text: 'This address will be used as your default delivery address',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, set as default'
      }).then((result) => {
        if (result.isConfirmed) {
          fetch(`/addresses/${addressId}/default`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            }
          })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: data.message,
                confirmButtonColor: '#6200ea'
              }).then(() => {
                window.location.reload();
              });
            } else {
              Swal.fire({
                icon: 'error',
                title: 'Error',
                text: data.message,
                confirmButtonColor: '#6200ea'
              });
            }
          })
          .catch(error => {
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: 'Failed to update default address',
              confirmButtonColor: '#6200ea'
            });
          });
        }
      });
    }

    function deleteAddress(addressId) {
      Swal.fire({
        title: 'Delete Address?',
        text: 'This action cannot be undone',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Yes, delete it'
      }).then((result) => {
        if (result.isConfirmed) {
          fetch(`/addresses/${addressId}`, {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
            }
          })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              Swal.fire({
                icon: 'success',
                title: 'Deleted!',
                text: data.message,
                confirmButtonColor: '#6200ea'
              }).then(() => {
                window.location.reload();
              });
            } else {
              Swal.fire({
                icon: 'error',
                title: 'Error',
                text: data.message,
                confirmButtonColor: '#6200ea'
              });
            }
          })
          .catch(error => {
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: 'Failed to delete address',
              confirmButtonColor: '#6200ea'
            });
          });
        }
      });
    }
  </script>
</body>
</html>
