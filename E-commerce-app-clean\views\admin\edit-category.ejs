<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Edit Category</title>
  <!-- SweetAlert2 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
  <style>
    :root {
      --primary: #4CAF50;
      --primary-dark: #388E3C;
      --error: #f44336;
      --text: #333;
      --light-gray: #f5f5f5;
      --border: #e0e0e0;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
      display: flex;
      min-height: 100vh;
      background-color: #f9f9f9;
      color: var(--text);
    }

    /* Sidebar Styles */
    .sidebar {
      width: 220px;
      background-color: white;
      border-right: 1px solid var(--border);
      padding: 20px 0;
      height: 100vh;
      position: sticky;
      top: 0;
    }

    .sidebar ul {
      list-style: none;
    }

    .sidebar li {
      padding: 12px 20px;
      cursor: pointer;
      transition: all 0.3s;
      border-left: 3px solid transparent;
    }

    .sidebar li:hover, .sidebar li.active {
      background-color: var(--light-gray);
      border-left: 3px solid var(--primary);
    }

    .sidebar a {
      text-decoration: none;
      color: inherit;
      display: block;
    }

    .logout-btn {
      background-color: black;
      color: white;
      padding: 12px;
      margin: 20px;
      text-align: center;
      cursor: pointer;
      border-radius: 4px;
    }

    /* Main Content Styles */
    .content {
      flex: 1;
      padding: 30px;
    }

    h1 {
      color: var(--primary);
      margin-bottom: 20px;
      font-weight: 600;
    }

    /* Form Styles */
    .form-container {
      background: white;
      border-radius: 8px;
      padding: 25px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      margin-bottom: 30px;
    }

    .form-row {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 15px;
      align-items: center;
    }

    .form-group {
      flex: 1;
      min-width: 200px;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
    }

    input, textarea {
      width: 100%;
      padding: 10px 15px;
      border: 1px solid var(--border);
      border-radius: 4px;
      font-size: 16px;
      transition: border 0.3s;
    }

    input:focus, textarea:focus {
      outline: none;
      border-color: var(--primary);
    }

    button {
      background-color: var(--primary);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      transition: background 0.3s;
    }

    button:hover {
      background-color: var(--primary-dark);
    }

    .back-btn {
      background-color: #ccc;
      margin-right: 10px;
    }

    .back-btn:hover {
      background-color: #bbb;
    }

    /* Messages */
    .alert {
      padding: 12px 15px;
      border-radius: 4px;
      margin-bottom: 20px;
    }

    .alert-success {
      background-color: #e8f5e9;
      color: var(--primary-dark);
      border: 1px solid #c8e6c9;
    }

    .alert-error {
      background-color: #ffebee;
      color: var(--error);
      border: 1px solid #ffcdd2;
    }

    /* Responsive Styles */
    @media (max-width: 768px) {
      body {
        flex-direction: column;
      }

      .sidebar {
        width: 100%;
        height: auto;
        position: relative;
      }

      .sidebar ul {
        display: flex;
        overflow-x: auto;
      }

      .sidebar li {
        white-space: nowrap;
      }

      .content {
        padding: 20px;
      }
    }
  </style>
</head>
<body>
  <div class="sidebar">
    <ul>
      <li><a href="/admin/dashboard">Dashboard</a></li>
      <li><a href="/admin/products">Products</a></li>
      <li><a href="/admin/orders">Order Lists</a></li>
      <li><a href="/admin/users">Users</a></li>
      <li><a href="/admin/sales">Sales</a></li>
      <li><a href="/admin/coupons">Coupons</a></li>
      <li class="active"><a href="/admin/category">Category</a></li>
      <li><a href="/admin/banner">Banner</a></li>
      <li><a href="/admin/offer">Offer</a></li>
    </ul>
    <div class="logout-btn">Logout</div>
  </div>

  <div class="content">
    <h1>Edit Category</h1>

    <% if (message) { %>
      <div class="alert alert-success"><%= message %></div>
    <% } %>

    <% if (error) { %>
      <div class="alert alert-error"><%= error %></div>
    <% } %>

    <div class="form-container">
      <h2>Edit Category Details</h2>
      <form id="editCategoryForm" action="/admin/editCategory/<%= category._id %>" method="POST">
        <div class="form-row">
          <div class="form-group">
            <label for="name">Category Name</label>
            <input type="text" id="name" name="name" required
                   value="<%= category.name %>"
                   placeholder="Enter category name" maxlength="30">
          </div>
          <div class="form-group">
            <label for="description">Category Description</label>
            <input type="text" id="description" name="description"
                   value="<%= category.description || '' %>"
                   placeholder="Enter description (optional)" maxlength="100">
          </div>
        </div>
        <button type="button" class="back-btn" onclick="window.location.href='/admin/category'">Back</button>
        <button type="submit">Update Category</button>
      </form>
    </div>
  </div>

  <!-- SweetAlert2 JS -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script>
    document.getElementById('editCategoryForm').addEventListener('submit', async function(e) {
      e.preventDefault();

      const nameInput = document.getElementById('name');
      const descriptionInput = document.getElementById('description');
      const nameValue = nameInput.value.trim();
      const descriptionValue = descriptionInput.value.trim();

      // Validation for Category Name
      if (!nameValue) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'Category name is required.',
          confirmButtonColor: '#4CAF50'
        });
        nameInput.focus();
        return;
      }

      if (nameValue.length < 2) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'Category name must be at least 2 characters long.',
          confirmButtonColor: '#4CAF50'
        });
        nameInput.focus();
        return;
      }

      if (/^\s+$/.test(nameValue)) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'Category name cannot be just spaces.',
          confirmButtonColor: '#4CAF50'
        });
        nameInput.focus();
        return;
      }

      // Allow letters, numbers, hyphens, and spaces, but no special characters
      if (!/^[a-zA-Z0-9\s-]+$/.test(nameValue)) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'Category name can only contain letters, numbers, spaces, and hyphens.',
          confirmButtonColor: '#4CAF50'
        });
        nameInput.focus();
        return;
      }

      // Validation for Category Description (optional)
      if (descriptionValue && /^\s+$/.test(descriptionValue)) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'Category description cannot be just spaces.',
          confirmButtonColor: '#4CAF50'
        });
        descriptionInput.focus();
        return;
      }

      // Show loading state
      Swal.fire({
        title: 'Updating category...',
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        }
      });

      try {
        const response = await fetch(this.action, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
          },
          body: new URLSearchParams({
            'name': nameValue,
            'description': descriptionValue
          }).toString()
        });

        const data = await response.json();
        console.log("dta",data)

        if (response.ok && data.success) {
          Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: data.message || 'Category updated successfully.',
            confirmButtonColor: '#4CAF50',
            timer: 1500,
            timerProgressBar: true
          }).then(() => {
            window.location.href = '/admin/category';
          });
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: data.message || 'Failed to update category.',
            confirmButtonColor: '#4CAF50'
          });
        }
      } catch (error) {
        console.error('Error updating category:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'An unexpected error occurred. Please try again.',
          confirmButtonColor: '#4CAF50'
        });
      }
    });
  </script>
</body>
</html>