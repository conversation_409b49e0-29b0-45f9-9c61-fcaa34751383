<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Category Management - Admin Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="/css/admin.css">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    body {
      background-color: #f8f9fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
    }

    .admin-header {
      background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
      color: white;
      padding: 2rem 0;
      margin-bottom: 2rem;
    }

    .stats-card {
      background: white;
      border-radius: 15px;
      padding: 1.5rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-bottom: 1rem;
      transition: transform 0.3s ease;
    }

    .stats-card:hover {
      transform: translateY(-5px);
    }

    .stats-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      color: white;
    }

    .form-section {
      background: white;
      border-radius: 15px;
      padding: 2rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
    }

    .category-card {
      background: white;
      border-radius: 15px;
      padding: 1.5rem;
      margin-bottom: 1rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }

    .category-card:hover {
      transform: translateY(-2px);
    }

    .category-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 1.2rem;
      font-weight: bold;
    }

    .status-toggle {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 30px;
    }

    .status-toggle input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 30px;
    }

    .slider:before {
      position: absolute;
      content: "";
      height: 22px;
      width: 22px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }

    input:checked + .slider {
      background-color: #17a2b8;
    }

    input:checked + .slider:before {
      transform: translateX(30px);
    }

    .action-btn {
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
      margin: 0.2rem;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .btn-edit {
      background: #ffc107;
      color: #212529;
    }

    .btn-edit:hover {
      background: #e0a800;
      color: #212529;
    }

    .btn-delete {
      background: #dc3545;
      color: white;
    }

    .btn-delete:hover {
      background: #c82333;
      color: white;
    }

    /* Table Styles */
    .table-container {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th, td {
      padding: 15px;
      text-align: left;
      border-bottom: 1px solid var(--border);
    }

    th {
      background-color: var(--primary);
      color: white;
      font-weight: 500;
    }

    tr:hover {
      background-color: var(--light-gray);
    }

    .status-toggle {
      display: inline-block;
      width: 50px;
      height: 24px;
      background: #ccc;
      border-radius: 12px;
      position: relative;
      cursor: pointer;
      transition: background 0.3s;
    }

    .status-toggle.active {
      background: var(--primary);
    }

    .status-toggle::after {
      content: '';
      position: absolute;
      width: 20px;
      height: 20px;
      background: white;
      border-radius: 50%;
      top: 2px;
      left: 2px;
      transition: transform 0.3s;
    }

    .status-toggle.active::after {
      transform: translateX(26px);
    }

    .status-toggle.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .action-btn {
      background: none;
      border: none;
      cursor: pointer;
      font-size: 18px;
      color: var(--text);
      transition: color 0.3s;
    }

    .action-btn:hover {
      color: var(--primary);
    }

    /* Messages */
    .alert {
      padding: 12px 15px;
      border-radius: 4px;
      margin-bottom: 20px;
    }

    .alert-success {
      background-color: #e8f5e9;
      color: var(--primary-dark);
      border: 1px solid #c8e6c9;
    }

    .alert-error {
      background-color: #ffebee;
      color: var(--error);
      border: 1px solid #ffcdd2;
    }

    /* Pagination Styles */
    .pagination {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 20px;
      gap: 10px;
    }

    .pagination-btn {
      background-color: var(--light-gray);
      color: var(--text);
      border: 1px solid var(--border);
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;
      font-size: 14px;
    }

    .pagination-btn:hover:not(.disabled) {
      background-color: var(--primary);
      color: white;
      border-color: var(--primary);
    }

    .pagination-btn.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .pagination-btn.active {
      background-color: var(--primary);
      color: white;
      border-color: var(--primary);
    }

    /* Responsive Styles */
    @media (max-width: 768px) {
      .content {
        padding: 20px;
      }

      table {
        display: block;
        overflow-x: auto;
      }

      .pagination {
        flex-wrap: wrap;
        gap: 5px;
      }

      .pagination-btn {
        padding: 6px 10px;
        font-size: 12px;
      }
    }

    /* Validation feedback styles */
    .invalid-feedback {
      display: block !important;
      width: 100%;
      margin-top: 0.25rem;
      font-size: 0.875rem;
      color: #dc3545;
    }

    .form-control.is-invalid {
      border-color: #dc3545;
      padding-right: calc(1.5em + 0.75rem);
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
      background-repeat: no-repeat;
      background-position: right calc(0.375em + 0.1875rem) center;
      background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }

    .form-control.is-valid {
      border-color: #198754;
      padding-right: calc(1.5em + 0.75rem);
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.17z'/%3e%3c/svg%3e");
      background-repeat: no-repeat;
      background-position: right calc(0.375em + 0.1875rem) center;
      background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }

    .form-control.is-invalid:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    .form-control.is-valid:focus {
      border-color: #198754;
      box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
    }
  </style>
</head>
<body>
  <!-- Include Admin Sidebar -->
  <%- include('partials/sidebar', { page: 'category' }) %>

  <div class="main-content">
    <!-- Admin Header -->
    <div class="admin-header">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-md-6">
            <h1><i class="fas fa-tags me-3"></i>Category Management</h1>
            <p class="mb-0">Organize and manage product categories</p>
          </div>
          <div class="col-md-6 text-end">
            <button class="btn btn-light" onclick="exportCategories()">
              <i class="fas fa-download me-2"></i>Export Categories
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="container">
    <!-- Category Statistics -->
    <div class="row mb-4">
      <div class="col-md-3 col-sm-6 col-12">
        <div class="stats-card text-center">
          <div class="stats-icon mx-auto mb-2" style="background: #17a2b8;">
            <i class="fas fa-tags"></i>
          </div>
          <h4><%= categories ? categories.length : 0 %></h4>
          <p class="mb-0">Total Categories</p>
        </div>
      </div>
      <div class="col-md-3 col-sm-6 col-12">
        <div class="stats-card text-center">
          <div class="stats-icon mx-auto mb-2" style="background: #28a745;">
            <i class="fas fa-eye"></i>
          </div>
          <h4><%= categories ? categories.filter(c => c.isListed).length : 0 %></h4>
          <p class="mb-0">Listed Categories</p>
        </div>
      </div>
      <div class="col-md-3 col-sm-6 col-12">
        <div class="stats-card text-center">
          <div class="stats-icon mx-auto mb-2" style="background: #dc3545;">
            <i class="fas fa-eye-slash"></i>
          </div>
          <h4><%= categories ? categories.filter(c => !c.isListed).length : 0 %></h4>
          <p class="mb-0">Unlisted Categories</p>
        </div>
      </div>
      <div class="col-md-3 col-sm-6 col-12">
        <div class="stats-card text-center">
          <div class="stats-icon mx-auto mb-2" style="background: #ffc107;">
            <i class="fas fa-plus"></i>
          </div>
          <h4>
            <%
              const today = new Date();
              const todayCategories = categories ? categories.filter(c => {
                const categoryDate = new Date(c.createdAt);
                return categoryDate.toDateString() === today.toDateString();
              }).length : 0;
            %>
            <%= todayCategories %>
          </h4>
          <p class="mb-0">Added Today</p>
        </div>
      </div>
    </div>

    <!-- Add Category Form -->
    <div class="form-section">
      <h3><i class="fas fa-plus-circle me-2"></i>Add New Category</h3>
      <form id="categoryForm" action="/admin/addCategory" method="POST" onsubmit="handleAddCategory(event)">
        <div class="row g-3">
          <div class="col-md-6">
            <label for="name" class="form-label">Category Name</label>
            <input type="text" class="form-control" id="name" name="name" required
                   placeholder="Enter category name" maxlength="30">
          </div>
          <div class="col-md-6">
            <label for="description" class="form-label">Category Description</label>
            <input type="text" class="form-control" id="description" name="description"
                   placeholder="Enter description (optional)" maxlength="100">
          </div>
          <div class="col-12">
            <button type="submit" class="btn btn-primary" id="addCategoryBtn">
              <i class="fas fa-plus me-2"></i>Add Category
            </button>
          </div>
        </div>
      </form>
    </div>

    <!-- Categories List -->
    <div class="mb-4">
      <h3><i class="fas fa-list me-2"></i>Categories List</h3>
    </div>

    <% if (categories.length === 0) { %>
      <div class="alert alert-info text-center">
        <i class="fas fa-info-circle me-2"></i>No categories found. Add your first category above!
      </div>
    <% } else { %>
      <div class="row">
        <% categories.forEach(category => { %>
          <div class="col-lg-6 col-xl-4 col-12">
            <div class="category-card">
              <div class="row align-items-center">
                <div class="col-auto">
                  <div class="category-icon">
                    <%= category.name.charAt(0).toUpperCase() %>
                  </div>
                </div>
                <div class="col">
                  <h5 class="mb-1"><%= category.name %></h5>
                  <p class="text-muted mb-1">
                    <%= category.description || 'No description provided' %>
                  </p>
                  <p class="text-muted mb-0">
                    <i class="fas fa-calendar me-1"></i>
                    Created <%= new Date(category.createdAt).toLocaleDateString() %>
                  </p>
                </div>
              </div>
              <hr>
              <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                  <label class="status-toggle me-2">
                    <input type="checkbox" <%= category.isListed ? 'checked' : '' %>
                           onchange="toggleCategoryStatus(this, '<%= category._id %>')">
                    <span class="slider"></span>
                  </label>
                  <span class="badge <%= category.isListed ? 'bg-success' : 'bg-secondary' %>">
                    <%= category.isListed ? 'Listed' : 'Unlisted' %>
                  </span>
                </div>
                <div>
                  <a href="/admin/editCategory?id=<%= category._id %>" class="action-btn btn-edit">
                    <i class="fas fa-edit me-1"></i>Edit
                  </a>
                  <button class="action-btn btn-delete" onclick="confirmDelete('<%= category._id %>')">
                    <i class="fas fa-trash me-1"></i>Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        <% }) %>
      </div>
    <% } %>

    <!-- Pagination -->
    <% if (totalPages > 1 && typeof currentPage !== 'undefined') { %>
      <div class="d-flex justify-content-center mt-4">
        <nav aria-label="Category pagination">
          <ul class="pagination">
            <% if (currentPage > 1) { %>
              <li class="page-item">
                <a class="page-link" href="/admin/category?page=<%= currentPage-1 %>">
                  <i class="fas fa-chevron-left"></i> Previous
                </a>
              </li>
            <% } %>

            <% for (let i = 1; i <= totalPages; i++) { %>
              <li class="page-item <%= i === currentPage ? 'active' : '' %>">
                <a class="page-link" href="/admin/category?page=<%= i %>">
                  <%= i %>
                </a>
              </li>
            <% } %>

            <% if (currentPage < totalPages) { %>
              <li class="page-item">
                <a class="page-link" href="/admin/category?page=<%= currentPage+1 %>">
                  Next <i class="fas fa-chevron-right"></i>
                </a>
              </li>
            <% } %>
          </ul>
        </nav>
      </div>
    <% } else if (typeof currentPage === 'undefined') { %>
      <div class="alert alert-danger text-center">
        <i class="fas fa-exclamation-triangle me-2"></i>Error: Unable to load pagination. Please try again.
      </div>
    <% } %>
    </div>
  </div>
  <script>
    // Check for success/error messages on page load
    document.addEventListener('DOMContentLoaded', function() {
      const urlParams = new URLSearchParams(window.location.search);
      const message = urlParams.get('message');
      const error = urlParams.get('error');

      if (message) {
        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: decodeURIComponent(message.replace(/\+/g, ' '))
        }).then(() => {
          // Clean URL by removing query parameters
          const url = new URL(window.location);
          url.searchParams.delete('message');
          url.searchParams.delete('error');
          window.history.replaceState({}, document.title, url.pathname + url.search);
        });
      } else if (error) {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: decodeURIComponent(error.replace(/\+/g, ' '))
        }).then(() => {
          // Clean URL by removing query parameters
          const url = new URL(window.location);
          url.searchParams.delete('message');
          url.searchParams.delete('error');
          window.history.replaceState({}, document.title, url.pathname + url.search);
        });
      }
    });

    function escapeHTML(str) {
      const div = document.createElement('div');
      div.textContent = str;
      return div.innerHTML;
    }


    async function handleAddCategory(event) {
      event.preventDefault(); // Always prevent default to handle validation first

      const form = document.getElementById('categoryForm');
      const nameInput = document.getElementById('name');
      const descriptionInput = document.getElementById('description');
      const nameValue = nameInput.value.trim();
      const descriptionValue = descriptionInput.value.trim();
      const addBtn = document.getElementById('addCategoryBtn');

      // Comprehensive client-side validation

      // 1. Required field validation
      if (!nameValue) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'Category name is required'
        });
        nameInput.focus();
        return false;
      }

      // 2. Length validation
      if (nameValue.length < 2) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'Category name must be at least 2 characters'
        });
        nameInput.focus();
        return false;
      }

      if (nameValue.length > 30) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'Category name must be less than 30 characters'
        });
        nameInput.focus();
        return false;
      }

      // 3. Space validation - cannot be only spaces
      if (/^\s+$/.test(nameValue)) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'Category name cannot contain only spaces'
        });
        nameInput.focus();
        return false;
      }

      // 4. Character validation - only letters, numbers, spaces, and hyphens
      if (!/^[a-zA-Z0-9\s-]+$/.test(nameValue)) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'Category name can only contain letters, numbers, spaces, and hyphens'
        });
        nameInput.focus();
        return false;
      }

      // 5. Cannot start or end with spaces
      if (nameValue !== nameValue.trim()) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'Category name cannot start or end with spaces'
        });
        nameInput.focus();
        return false;
      }

      // 6. Cannot have multiple consecutive spaces
      if (/\s{2,}/.test(nameValue)) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'Category name cannot have multiple consecutive spaces'
        });
        nameInput.focus();
        return false;
      }

      // 7. Must start with a letter
      if (!/^[a-zA-Z]/.test(nameValue)) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'Category name must start with a letter'
        });
        nameInput.focus();
        return false;
      }

      // 8. Check for duplicate names (case-insensitive)
      const existingCategories = Array.from(document.querySelectorAll('#categoryTableBody tr td:first-child'))
        .map(td => td.textContent.trim().toLowerCase());

      if (existingCategories.includes(nameValue.toLowerCase())) {
        Swal.fire({
          icon: 'error',
          title: 'Duplicate Category',
          text: 'A category with this name already exists (case-insensitive check)'
        });
        nameInput.focus();
        return false;
      }

      // 9. Description validation (if provided)
      if (descriptionValue) {
        if (descriptionValue.length > 100) {
          Swal.fire({
            icon: 'error',
            title: 'Validation Error',
            text: 'Description must be less than 100 characters'
          });
          descriptionInput.focus();
          return false;
        }

        if (/^\s+$/.test(descriptionValue)) {
          Swal.fire({
            icon: 'error',
            title: 'Validation Error',
            text: 'Description cannot contain only spaces'
          });
          descriptionInput.focus();
          return false;
        }
      }

      // If all validations pass, show loading and submit form
      addBtn.disabled = true;
      addBtn.textContent = 'Adding...';

      Swal.fire({
        title: 'Adding Category...',
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        }
      });

      // Submit the form programmatically
      form.submit();
      return true;
    }

    // Real-time validation for category name
    document.addEventListener('DOMContentLoaded', function() {
      const nameInput = document.getElementById('name');
      const descriptionInput = document.getElementById('description');

      // Add validation feedback elements
      if (!document.getElementById('name-feedback')) {
        const nameFeedback = document.createElement('div');
        nameFeedback.id = 'name-feedback';
        nameFeedback.className = 'invalid-feedback';
        nameFeedback.style.display = 'none';
        nameInput.parentNode.appendChild(nameFeedback);
      }

      if (!document.getElementById('description-feedback')) {
        const descFeedback = document.createElement('div');
        descFeedback.id = 'description-feedback';
        descFeedback.className = 'invalid-feedback';
        descFeedback.style.display = 'none';
        descriptionInput.parentNode.appendChild(descFeedback);
      }

      // Real-time validation for name input
      nameInput.addEventListener('input', function() {
        const value = this.value;
        const trimmedValue = value.trim();
        const feedback = document.getElementById('name-feedback');

        // Reset styles
        this.classList.remove('is-invalid', 'is-valid');
        feedback.style.display = 'none';

        if (value.length === 0) {
          return; // Don't show validation for empty field while typing
        }

        let isValid = true;
        let errorMessage = '';

        // Check various validation rules
        if (trimmedValue.length < 2) {
          isValid = false;
          errorMessage = 'Must be at least 2 characters';
        } else if (trimmedValue.length > 30) {
          isValid = false;
          errorMessage = 'Must be less than 30 characters';
        } else if (/^\s+$/.test(value)) {
          isValid = false;
          errorMessage = 'Cannot contain only spaces';
        } else if (!/^[a-zA-Z0-9\s-]*$/.test(trimmedValue)) {
          isValid = false;
          errorMessage = 'Only letters, numbers, spaces, and hyphens allowed';
        } else if (!/^[a-zA-Z]/.test(trimmedValue)) {
          isValid = false;
          errorMessage = 'Must start with a letter';
        } else if (/\s{2,}/.test(trimmedValue)) {
          isValid = false;
          errorMessage = 'Cannot have multiple consecutive spaces';
        } else {
          // Check for duplicates (case-insensitive)
          const existingCategories = Array.from(document.querySelectorAll('#categoryTableBody tr td:first-child'))
            .map(td => td.textContent.trim().toLowerCase());

          if (existingCategories.includes(trimmedValue.toLowerCase())) {
            isValid = false;
            errorMessage = 'Category name already exists';
          }
        }

        // Apply validation feedback
        if (isValid) {
          this.classList.add('is-valid');
        } else {
          this.classList.add('is-invalid');
          feedback.textContent = errorMessage;
          feedback.style.display = 'block';
        }
      });

      // Real-time validation for description
      descriptionInput.addEventListener('input', function() {
        const value = this.value;
        const trimmedValue = value.trim();
        const feedback = document.getElementById('description-feedback');

        // Reset styles
        this.classList.remove('is-invalid', 'is-valid');
        feedback.style.display = 'none';

        if (value.length === 0) {
          return; // Don't show validation for empty field
        }

        let isValid = true;
        let errorMessage = '';

        if (value.length > 100) {
          isValid = false;
          errorMessage = 'Must be less than 100 characters';
        } else if (/^\s+$/.test(value)) {
          isValid = false;
          errorMessage = 'Cannot contain only spaces';
        }

        // Apply validation feedback
        if (isValid) {
          this.classList.add('is-valid');
        } else {
          this.classList.add('is-invalid');
          feedback.textContent = errorMessage;
          feedback.style.display = 'block';
        }
      });

      // Debounced AJAX check for duplicate category names
      let debounceTimer;
      nameInput.addEventListener('input', function() {
        const value = this.value.trim();

        if (value.length < 2) return;

        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(async () => {
          try {
            const response = await fetch(`/admin/check-category-name?name=${encodeURIComponent(value)}`, {
              method: 'GET',
              headers: {
                'Accept': 'application/json'
              }
            });

            if (response.ok) {
              const data = await response.json();
              const feedback = document.getElementById('name-feedback');

              if (data.exists) {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
                feedback.textContent = 'Category name already exists';
                feedback.style.display = 'block';
              } else if (this.classList.contains('is-invalid') && feedback.textContent === 'Category name already exists') {
                // Clear the duplicate error if it was the only issue
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
                feedback.style.display = 'none';
              }
            }
          } catch (error) {
            console.log('Could not check for duplicate category names:', error);
            // Fail silently for duplicate check
          }
        }, 500);
      });
    });

    // Toggle category status
    async function toggleCategoryStatus(element, categoryId) {
      try {
        const isChecked = element.checked;
        const originalState = !isChecked;

        // Show loading state
        element.disabled = true;

        const response = await fetch(`/admin/category/toggle/${categoryId}`, {
          method: 'PATCH',
          headers: {
            'Accept': 'application/json'
          }
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to update category status');
        }

        const data = await response.json();
        const newStatus = data.newStatus;

        // Update the badge
        const badge = element.closest('.category-card').querySelector('.badge');
        if (newStatus) {
          badge.className = 'badge bg-success';
          badge.innerHTML = 'Listed';
        } else {
          badge.className = 'badge bg-secondary';
          badge.innerHTML = 'Unlisted';
        }

        // Show success message
        Swal.fire({
          title: 'Success!',
          text: `Category ${newStatus ? 'listed' : 'unlisted'} successfully.`,
          icon: 'success',
          timer: 1500,
          showConfirmButton: false
        });

      } catch (error) {
        console.error('Error:', error);
        // Revert the toggle state
        element.checked = !element.checked;

        Swal.fire({
          title: 'Error!',
          text: error.message || 'Failed to update category status',
          icon: 'error',
          confirmButtonColor: '#dc3545'
        });
      } finally {
        element.disabled = false;
      }
    }

    // Confirm delete with SweetAlert
    async function confirmDelete(categoryId) {
      const result = await Swal.fire({
        title: 'Are you sure?',
        text: "You won't be able to revert this!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'Cancel'
      });

      if (result.isConfirmed) {
        try {
          const response = await fetch('/admin/deleteCategory', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `_method=DELETE&id=${categoryId}`
          });

          if (response.ok) {
            await Swal.fire({
              title: 'Deleted!',
              text: 'Category has been deleted successfully.',
              icon: 'success',
              timer: 1500,
              showConfirmButton: false
            });
            location.reload();
          } else {
            throw new Error('Failed to delete category');
          }
        } catch (error) {
          Swal.fire({
            title: 'Error!',
            text: 'Failed to delete category. Please try again.',
            icon: 'error',
            confirmButtonColor: '#dc3545'
          });
        }
      }
    }

    function exportCategories() {
      Swal.fire({
        title: 'Export Categories',
        text: 'This feature will be available soon!',
        icon: 'info',
        confirmButtonColor: '#17a2b8'
      });
    }
  </script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>