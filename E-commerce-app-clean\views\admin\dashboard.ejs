<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Admin Dashboard</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="/css/admin.css">
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    .top-bar {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 20px;
    }

    .search-input {
      padding: 8px;
      border-radius: 5px;
      border: 1px solid #ccc;
      width: 250px;
    }

    h2 {
      font-size: 1.8em;
      margin-bottom: 20px;
    }

    .stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .card {
      background: white;
      padding: 20px;
      border-radius: 10px;
      font-size: 1.1em;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .card span {
      display: block;
      margin-top: 10px;
      font-weight: bold;
      font-size: 1.5em;
    }

    .sales-section {
      background: #dff4e0;
      padding: 20px;
      border-radius: 15px;
    }

    .sales-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    #salesChart {
      width: 100%;
      height: 300px;
    }

    a {
      text-decoration: none;
      color: inherit;
      cursor: pointer;
    }

    @media (max-width: 768px) {
      .top-bar {
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <!-- Include Admin Sidebar -->
  <%- include('partials/sidebar', { page: 'dashboard' }) %>

  <div class="main-content">
      <div class="top-bar">
        <input type="text" placeholder="Search" class="search-input" />
      </div>

      <h2>Dashboard</h2>

      <div class="stats">
        <div class="card">Total Users <span>10,583</span></div>
        <div class="card">Total Orders <span>8,538</span></div>
        <div class="card">Total Sales <span>₹65,805</span></div>
        <div class="card">Total Pending <span>3,552</span></div>
      </div>

      <!-- Quick Access Section -->
      <div style="margin: 2rem 0;">
        <h3 style="margin-bottom: 1rem; color: #333;">Quick Access</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
          <a href="/admin/inventory" style="text-decoration: none;">
            <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 1.5rem; border-radius: 10px; text-align: center; transition: transform 0.3s ease; cursor: pointer;" onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
              <i class="fas fa-boxes" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
              <h4 style="margin: 0; font-size: 1.1rem;">Inventory Management</h4>
              <p style="margin: 0.5rem 0 0 0; font-size: 0.9rem; opacity: 0.9;">Manage stock & products</p>
            </div>
          </a>
          <a href="/admin/inventory/alerts" style="text-decoration: none;">
            <div style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white; padding: 1.5rem; border-radius: 10px; text-align: center; transition: transform 0.3s ease; cursor: pointer;" onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
              <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
              <h4 style="margin: 0; font-size: 1.1rem;">Stock Alerts</h4>
              <p style="margin: 0.5rem 0 0 0; font-size: 0.9rem; opacity: 0.9;">Low & out of stock</p>
            </div>
          </a>
          <a href="/admin/orders" style="text-decoration: none;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 1.5rem; border-radius: 10px; text-align: center; transition: transform 0.3s ease; cursor: pointer;" onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
              <i class="fas fa-shopping-cart" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
              <h4 style="margin: 0; font-size: 1.1rem;">Order Management</h4>
              <p style="margin: 0.5rem 0 0 0; font-size: 0.9rem; opacity: 0.9;">Manage customer orders</p>
            </div>
          </a>
          <a href="/admin/return-requests" style="text-decoration: none;">
            <div style="background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%); color: white; padding: 1.5rem; border-radius: 10px; text-align: center; transition: transform 0.3s ease; cursor: pointer;" onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
              <i class="fas fa-undo" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
              <h4 style="margin: 0; font-size: 1.1rem;">Return Requests</h4>
              <p style="margin: 0.5rem 0 0 0; font-size: 0.9rem; opacity: 0.9;">Handle return requests</p>
            </div>
          </a>
        </div>
      </div>

      <div class="sales-section">
        <div class="sales-header">
          <h3>Sales Details</h3>
          <select id="monthSelect">
            <option>October</option>
            <option>November</option>
            <option>December</option>
          </select>
        </div>
        <canvas id="salesChart"></canvas>
      </div>
  </div>

  <!-- Chart.js CDN -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    const ctx = document.getElementById('salesChart').getContext('2d');
    const salesChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: Array.from({ length: 60 }, (_, i) => `${i + 1}k`),
        datasets: [{
          label: 'Sales',
          data: Array.from({ length: 60 }, () => Math.floor(Math.random() * 100)),
          borderColor: 'green',
          fill: true,
          backgroundColor: 'rgba(0,255,0,0.1)',
          tension: 0.4,
          pointBackgroundColor: 'purple',
        }]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            max: 100,
          }
        }
      }
    });

    // Dropdown event listener
    document.getElementById('monthSelect').addEventListener('change', function () {
      alert(`Loading sales data for ${this.value}`);
    });
  </script>
</body>
</html>
