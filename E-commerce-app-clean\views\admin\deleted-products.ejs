<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deleted Products - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/admin.css">
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <style>
        .product-img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
            opacity: 0.7;
        }
        .deleted-product-row {
            background-color: #f8f9fa;
            opacity: 0.8;
        }
        .deleted-badge {
            background-color: #dc3545;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
        }
        .restore-btn {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
        }
        .restore-btn:hover {
            background-color: #218838;
            border-color: #1e7e34;
            color: white;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }
        .pagination-btn {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #e0e0e0;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }
        .pagination-btn:hover:not(.disabled) {
            background-color: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }
        .pagination-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .pagination-btn.active {
            background-color: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }
    </style>
</head>
<body>
    <!-- Include Admin Sidebar -->
    <%- include('partials/sidebar', { page: 'products' }) %>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid mt-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>Deleted Products</h2>
                    <p class="text-muted mb-0">Manage soft-deleted products - restore or permanently delete</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="/admin/products" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left"></i> Back to Products
                    </a>
                </div>
            </div>

            <% if (error) { %>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <%= error %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <% } %>

            <% if (typeof message !== 'undefined' && message) { %>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <%= message %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <% } %>

            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-trash text-danger"></i> 
                            Deleted Products (<%= totalProducts %>)
                        </h5>
                        <% if (totalProducts > 0) { %>
                            <small class="text-muted">
                                Showing <%= ((currentPage - 1) * 4) + 1 %>-<%= Math.min(currentPage * 4, totalProducts) %> of <%= totalProducts %> products
                            </small>
                        <% } %>
                    </div>
                </div>
                <div class="card-body">
                    <% if (products.length === 0) { %>
                        <div class="text-center py-5">
                            <i class="bi bi-trash text-muted" style="font-size: 3rem;"></i>
                            <h4 class="mt-3 text-muted">No Deleted Products</h4>
                            <p class="text-muted">All products are active. Deleted products will appear here.</p>
                            <a href="/admin/products" class="btn btn-primary">
                                <i class="bi bi-arrow-left"></i> Back to Products
                            </a>
                        </div>
                    <% } else { %>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Image</th>
                                        <th>Name</th>
                                        <th>Category</th>
                                        <th>Price</th>
                                        <th>Deleted Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="deletedProductsTableBody">
                                    <% products.forEach(product => { %>
                                        <tr class="deleted-product-row">
                                            <td>
                                                <% if (product.productImage && product.productImage.length > 0) { %>
                                                    <img src="/uploads/product-images/<%= product.productImage[0] %>" class="product-img" alt="<%= product.productName %>">
                                                <% } else { %>
                                                    <div class="product-img bg-light d-flex align-items-center justify-content-center">
                                                        <i class="bi bi-image text-muted"></i>
                                                    </div>
                                                <% } %>
                                            </td>
                                            <td>
                                                <div>
                                                    <%= product.productName || 'N/A' %>
                                                    <br>
                                                    <span class="deleted-badge">DELETED</span>
                                                </div>
                                            </td>
                                            <td><%= product.category && product.category.name ? product.category.name : 'N/A' %></td>
                                            <td>$<%= product.price ? product.price.toFixed(2) : '0.00' %></td>
                                            <td>
                                                <% if (product.deletedAt) { %>
                                                    <%= new Date(product.deletedAt).toLocaleDateString() %>
                                                    <br>
                                                    <small class="text-muted"><%= new Date(product.deletedAt).toLocaleTimeString() %></small>
                                                <% } else { %>
                                                    <span class="text-muted">Unknown</span>
                                                <% } %>
                                            </td>
                                            <td class="action-btns">
                                                <button class="btn btn-sm restore-btn me-1" onclick="restoreProduct('<%= product._id %>', '<%= product.productName %>')">
                                                    <i class="bi bi-arrow-clockwise"></i> Restore
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="permanentlyDeleteProduct('<%= product._id %>', '<%= product.productName %>')">
                                                    <i class="bi bi-trash"></i> Permanent Delete
                                                </button>
                                            </td>
                                        </tr>
                                    <% }) %>
                                </tbody>
                            </table>
                        </div>
                    <% } %>
                </div>
            </div>

            <!-- Pagination Controls -->
            <% if (totalPages > 1) { %>
                <div class="pagination" id="paginationControls">
                    <button class="pagination-btn <%= currentPage === 1 ? 'disabled' : '' %>"
                            onclick="loadPage(<%= Math.max(1, currentPage - 1) %>)"
                            <% if (currentPage === 1) { %> disabled <% } %>>
                        Previous
                    </button>

                    <% for (let i = 1; i <= totalPages; i++) { %>
                        <button class="pagination-btn <%= currentPage === i ? 'active' : '' %>"
                                onclick="loadPage(<%= i %>)">
                            <%= i %>
                        </button>
                    <% } %>

                    <button class="pagination-btn <%= currentPage === totalPages ? 'disabled' : '' %>"
                            onclick="loadPage(<%= Math.min(totalPages, currentPage + 1) %>)"
                            <% if (currentPage === totalPages) { %> disabled <% } %>>
                        Next
                    </button>
                </div>
            <% } %>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Load page function for pagination
        async function loadPage(page) {
            try {
                window.location.href = `/admin/deleted-products?page=${page}`;
            } catch (error) {
                console.error('Error loading page:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to load page. Please try again.'
                });
            }
        }

        // Restore product function
        async function restoreProduct(productId, productName) {
            const result = await Swal.fire({
                title: 'Restore Product?',
                html: `Are you sure you want to restore <strong>"${productName}"</strong>?<br><br>
                       <small class="text-muted">This will make the product available again in the store.</small>`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="bi bi-arrow-clockwise"></i> Yes, restore it!',
                cancelButtonText: '<i class="bi bi-x-circle"></i> Cancel',
                reverseButtons: true
            });

            if (!result.isConfirmed) return;

            try {
                Swal.fire({
                    title: 'Restoring Product...',
                    html: `Restoring "${productName}"...`,
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                const response = await fetch('/admin/restore-product', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ id: productId })
                });

                const data = await response.json();

                if (data.success) {
                    await Swal.fire({
                        icon: 'success',
                        title: 'Product Restored!',
                        html: `<strong>"${data.productName || productName}"</strong> has been restored successfully.<br><br>
                               <small class="text-muted">The product is now available in the store again.</small>`,
                        confirmButtonColor: '#28a745',
                        confirmButtonText: '<i class="bi bi-check-circle"></i> OK'
                    });

                    // Reload the page
                    window.location.reload();
                } else {
                    await Swal.fire({
                        icon: 'error',
                        title: 'Restore Failed',
                        text: data.message || 'Failed to restore product',
                        confirmButtonColor: '#dc3545'
                    });
                }
            } catch (error) {
                console.error('Error restoring product:', error);
                await Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'An unexpected error occurred. Please try again.',
                    confirmButtonColor: '#dc3545'
                });
            }
        }

        // Permanent delete function (placeholder for future implementation)
        async function permanentlyDeleteProduct(productId, productName) {
            await Swal.fire({
                icon: 'info',
                title: 'Feature Coming Soon',
                text: 'Permanent deletion feature will be available in a future update.',
                confirmButtonColor: '#6c757d'
            });
        }
    </script>
</body>
</html>
