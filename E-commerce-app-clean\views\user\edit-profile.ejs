<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Edit Profile | Luxe Scents</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.6.1/cropper.min.css">
  <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    body {
      background-color: #f8f9fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .navbar {
      background-color: #000000;
      padding: 1rem 2rem;
      border-bottom: 1px solid #090909;
    }
    .navbar-brand {
      font-weight: 700;
      font-size: 1.8rem;
      color: #ffffff !important;
      letter-spacing: 1px;
    }
    .nav-link {
      color: #e0e0e0 !important;
      font-weight: 500;
      margin: 0 1rem;
      transition: color 0.3s;
    }
    .nav-link:hover {
      color: #bb86fc !important;
    }
    .edit-container {
      max-width: 800px;
      margin: 2rem auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    .edit-header {
      background: linear-gradient(135deg, #6200ea, #bb86fc);
      color: white;
      padding: 2rem;
      text-align: center;
    }
    .edit-body {
      padding: 2rem;
    }
    .form-section {
      margin-bottom: 2rem;
    }
    .form-section h5 {
      color: #6200ea;
      border-bottom: 2px solid #6200ea;
      padding-bottom: 0.5rem;
      margin-bottom: 1rem;
    }
    .form-control {
      border-radius: 10px;
      border: 2px solid #e9ecef;
      padding: 0.75rem 1rem;
      transition: all 0.3s;
    }
    .form-control:focus {
      border-color: #6200ea;
      box-shadow: 0 0 0 0.2rem rgba(98, 0, 234, 0.25);
    }
    .btn-primary {
      background-color: #6200ea;
      border-color: #6200ea;
      border-radius: 25px;
      padding: 0.75rem 2rem;
      font-weight: 500;
      transition: all 0.3s;
    }
    .btn-primary:hover {
      background-color: #5300d1;
      transform: translateY(-2px);
    }
    .btn-secondary {
      border-radius: 25px;
      padding: 0.75rem 2rem;
      font-weight: 500;
      transition: all 0.3s;
    }
    .btn-secondary:hover {
      transform: translateY(-2px);
    }
    .profile-image-section {
      text-align: center;
      margin-bottom: 2rem;
    }
    .current-image {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      border: 4px solid #6200ea;
      object-fit: cover;
      margin-bottom: 1rem;
    }
    .image-placeholder {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      border: 4px solid #6200ea;
      background-color: #f8f9fa;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1rem;
      color: #6200ea;
    }
    .alert {
      border-radius: 10px;
      border: none;
    }
    .email-change-section {
      background-color: #f8f9fa;
      padding: 1.5rem;
      border-radius: 10px;
      margin-top: 1rem;
    }
    .action-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin-top: 2rem;
    }
    @media (max-width: 768px) {
      .edit-container {
        margin: 1rem;
      }
      .action-buttons {
        flex-direction: column;
      }
    }

    /* Cropper.js Styles */
    .crop-modal .modal-body {
      padding: 0;
    }

    .crop-container {
      max-height: 400px;
      overflow: hidden;
    }

    .crop-container img {
      max-width: 100%;
      height: auto;
    }

    .crop-controls {
      padding: 15px;
      background: #f8f9fa;
      border-top: 1px solid #dee2e6;
    }

    .cropped-preview {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      object-fit: cover;
      border: 4px solid #28a745;
      margin-bottom: 1rem;
    }

    .crop-btn {
      background: linear-gradient(135deg, #6200ea 0%, #3700b3 100%);
      border: none;
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 0.9em;
      transition: all 0.3s;
    }

    .crop-btn:hover {
      background: linear-gradient(135deg, #3700b3 0%, #6200ea 100%);
      transform: translateY(-2px);
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
      <a class="navbar-brand" href="/">LUXE SCENTS</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <a class="nav-link" href="/">Home</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/shop">Shop</a>
          </li>
          <li class="nav-item">
            <a class="nav-link active" href="/profile">Profile</a>
          </li>
        </ul>
        <div class="navbar-icons">
          <div class="dropdown">
            <a href="#" class="dropdown-toggle text-white" role="button" data-bs-toggle="dropdown">
              <i class="fas fa-user-circle"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li><a class="dropdown-item" href="/profile">My Profile</a></li>
              <li><a class="dropdown-item" href="#" onclick="confirmLogout(event)">Logout</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- Edit Profile Container -->
  <div class="edit-container">
    <!-- Header -->
    <div class="edit-header">
      <h2><i class="fas fa-edit me-2"></i>Edit Profile</h2>
      <p class="mb-0">Update your personal information and preferences</p>
    </div>

    <!-- Body -->
    <div class="edit-body">
      <!-- Success/Error Messages -->
      <% if (message) { %>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
          <%= message %>
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      <% } %>
      <% if (error) { %>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
          <%= error %>
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      <% } %>

      <form action="/profile/edit" method="POST" enctype="multipart/form-data" id="editProfileForm">
        <!-- Profile Image Section -->
        <div class="profile-image-section">
          <div id="currentImageContainer">
            <% if (user.profileImage) { %>
              <img src="/uploads/profile-images/<%= user.profileImage %>" alt="Current Profile Image" class="current-image" id="currentImage">
            <% } else { %>
              <div class="image-placeholder" id="imagePlaceholder">
                <i class="fas fa-user fa-3x"></i>
              </div>
            <% } %>
          </div>

          <div class="mb-3">
            <label for="profileImage" class="form-label">Profile Image</label>
            <input type="file" class="form-control" id="profileImage" name="profileImage" accept="image/*">
            <div class="form-text">Choose a new profile image to crop (JPG, PNG, WEBP - Max 2MB)</div>
            <button type="button" class="btn crop-btn mt-2" id="cropImageBtn" style="display: none;">
              <i class="fas fa-crop me-2"></i>Crop Image
            </button>
          </div>

          <!-- Hidden input to store cropped image data -->
          <input type="hidden" id="croppedImageData" name="croppedImageData">
        </div>

        <!-- Personal Information -->
        <div class="form-section">
          <h5><i class="fas fa-user me-2"></i>Personal Information</h5>
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="name" class="form-label">Full Name</label>
              <input type="text" class="form-control" id="name" name="name" value="<%= user.name || '' %>" required>
            </div>
            <div class="col-md-6 mb-3">
              <label for="phone" class="form-label">Phone Number</label>
              <input type="tel" class="form-control" id="phone" name="phone" value="<%= user.phone || '' %>" placeholder="+91XXXXXXXXXX">
            </div>
          </div>
        </div>

        <!-- Address Information -->
        <div class="form-section">
          <h5><i class="fas fa-map-marker-alt me-2"></i>Address Information</h5>
          <div class="mb-3">
            <label for="street" class="form-label">Street Address</label>
            <input type="text" class="form-control" id="street" name="street" value="<%= user.address?.street || '' %>">
          </div>
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="city" class="form-label">City</label>
              <input type="text" class="form-control" id="city" name="city" value="<%= user.address?.city || '' %>">
            </div>
            <div class="col-md-6 mb-3">
              <label for="state" class="form-label">State</label>
              <input type="text" class="form-control" id="state" name="state" value="<%= user.address?.state || '' %>">
            </div>
          </div>
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="zipCode" class="form-label">ZIP Code</label>
              <input type="text" class="form-control" id="zipCode" name="zipCode" value="<%= user.address?.zipCode || '' %>">
            </div>
            <div class="col-md-6 mb-3">
              <label for="country" class="form-label">Country</label>
              <input type="text" class="form-control" id="country" name="country" value="<%= user.address?.country || '' %>">
            </div>
          </div>
        </div>

        <!-- Email Change Section -->
        <div class="form-section">
          <h5><i class="fas fa-envelope me-2"></i>Email Address</h5>
          <p class="text-muted">Current Email: <strong><%= user.email %></strong></p>
          <div class="email-change-section">
            <p class="mb-2"><strong>Want to change your email?</strong></p>
            <p class="text-muted small">Email changes require verification for security. Click the button below to request an email change.</p>
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="requestEmailChange()">
              <i class="fas fa-envelope me-2"></i>Change Email
            </button>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-2"></i>Save Changes
          </button>
          <a href="/profile" class="btn btn-secondary">
            <i class="fas fa-times me-2"></i>Cancel
          </a>
        </div>
      </form>
    </div>
  </div>

  <!-- Profile Image Crop Modal -->
  <div class="modal fade crop-modal" id="profileCropModal" tabindex="-1" aria-labelledby="profileCropModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="profileCropModalLabel">
            <i class="fas fa-crop me-2"></i>Crop Profile Image
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="crop-container">
            <img id="profileCropImage" style="max-width: 100%;">
          </div>
        </div>
        <div class="crop-controls">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <button type="button" class="btn btn-outline-secondary btn-sm" onclick="rotateProfileCropper(-90)">
                <i class="fas fa-undo"></i> Rotate Left
              </button>
              <button type="button" class="btn btn-outline-secondary btn-sm" onclick="rotateProfileCropper(90)">
                <i class="fas fa-redo"></i> Rotate Right
              </button>
              <button type="button" class="btn btn-outline-secondary btn-sm" onclick="resetProfileCropper()">
                <i class="fas fa-refresh"></i> Reset
              </button>
            </div>
            <div>
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
              <button type="button" class="btn btn-primary" onclick="cropAndSaveProfile()">
                <i class="fas fa-check me-1"></i>Crop & Save
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.6.1/cropper.min.js"></script>
  <script>
    let profileCropper = null;

    // Enhanced form validation for edit profile
    document.addEventListener('DOMContentLoaded', function() {
      const nameInput = document.getElementById('name');
      const phoneInput = document.getElementById('phone');
      const form = document.getElementById('editProfileForm');

      // Name validation (no underscores allowed in profile)
      nameInput.addEventListener('input', function() {
        const value = this.value.trim();

        if (value.length === 0) return;

        // Check for underscores (not allowed in profile)
        if (value.includes('_')) {
          Swal.fire({
            icon: 'error',
            title: 'Invalid Name Format',
            text: 'Name cannot contain underscores. Please use only letters and spaces.',
            confirmButtonColor: '#6200ea'
          });
          // Remove underscores
          this.value = value.replace(/_/g, '');
          return;
        }

        // Check for invalid characters
        if (!/^[A-Za-z\s]+$/.test(value)) {
          Swal.fire({
            icon: 'error',
            title: 'Invalid Name Format',
            text: 'Name can only contain letters and spaces.',
            confirmButtonColor: '#6200ea'
          });
          // Remove invalid characters
          this.value = value.replace(/[^A-Za-z\s]/g, '');
          return;
        }

        // Check for multiple consecutive spaces
        if (/\s{2,}/.test(value)) {
          Swal.fire({
            icon: 'error',
            title: 'Invalid Name Format',
            text: 'Name cannot have multiple consecutive spaces.',
            confirmButtonColor: '#6200ea'
          });
          // Replace multiple spaces with single space
          this.value = value.replace(/\s{2,}/g, ' ');
          return;
        }
      });

      // Phone validation
      phoneInput.addEventListener('input', function() {
        const value = this.value.trim();

        if (value.length === 0) return;

        // Check for underscores (not allowed)
        if (value.includes('_')) {
          Swal.fire({
            icon: 'error',
            title: 'Invalid Phone Format',
            text: 'Phone number cannot contain underscores.',
            confirmButtonColor: '#6200ea'
          });
          // Remove underscores
          this.value = value.replace(/_/g, '');
          return;
        }

        // Validate phone format
        if (value && !/^\+91[6-9]\d{9}$/.test(value)) {
          // Don't show error while typing, just on blur
        }
      });

      phoneInput.addEventListener('blur', function() {
        const value = this.value.trim();

        if (value && !/^\+91[6-9]\d{9}$/.test(value)) {
          Swal.fire({
            icon: 'error',
            title: 'Invalid Phone Number',
            text: 'Please enter a valid Indian mobile number in format +91XXXXXXXXXX',
            confirmButtonColor: '#6200ea'
          });
        }
      });

      // Form submission validation
      form.addEventListener('submit', function(e) {
        const name = nameInput.value.trim();
        const phone = phoneInput.value.trim();

        // Validate name
        if (!name) {
          e.preventDefault();
          Swal.fire({
            icon: 'warning',
            title: 'Name Required',
            text: 'Please enter your full name',
            confirmButtonColor: '#6200ea'
          });
          return;
        }

        if (name.includes('_')) {
          e.preventDefault();
          Swal.fire({
            icon: 'error',
            title: 'Invalid Name Format',
            text: 'Name cannot contain underscores. Please use only letters and spaces.',
            confirmButtonColor: '#6200ea'
          });
          return;
        }

        if (!/^[A-Za-z\s]+$/.test(name)) {
          e.preventDefault();
          Swal.fire({
            icon: 'error',
            title: 'Invalid Name Format',
            text: 'Name can only contain letters and spaces.',
            confirmButtonColor: '#6200ea'
          });
          return;
        }

        // Validate phone if provided
        if (phone && !/^\+91[6-9]\d{9}$/.test(phone)) {
          e.preventDefault();
          Swal.fire({
            icon: 'error',
            title: 'Invalid Phone Number',
            text: 'Please enter a valid Indian mobile number in format +91XXXXXXXXXX',
            confirmButtonColor: '#6200ea'
          });
          return;
        }
      });
    });
    let selectedProfileFile = null;

    // Profile image selection and cropping
    document.getElementById('profileImage').addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (!file) return;

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        Swal.fire({
          icon: 'error',
          title: 'Invalid File Type',
          text: 'Please select a valid image file (JPG, PNG, WEBP)',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Validate file size (2MB)
      if (file.size > 2 * 1024 * 1024) {
        Swal.fire({
          icon: 'error',
          title: 'File Too Large',
          text: 'Please select an image smaller than 2MB',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      selectedProfileFile = file;
      document.getElementById('cropImageBtn').style.display = 'inline-block';
    });

    // Show crop modal
    document.getElementById('cropImageBtn').addEventListener('click', function() {
      if (!selectedProfileFile) return;

      const reader = new FileReader();
      reader.onload = function(e) {
        const cropImage = document.getElementById('profileCropImage');
        cropImage.src = e.target.result;

        // Show crop modal
        const cropModal = new bootstrap.Modal(document.getElementById('profileCropModal'));
        cropModal.show();

        // Initialize cropper when modal is shown
        document.getElementById('profileCropModal').addEventListener('shown.bs.modal', function() {
          if (profileCropper) {
            profileCropper.destroy();
          }
          profileCropper = new Cropper(cropImage, {
            aspectRatio: 1,
            viewMode: 1,
            autoCropArea: 0.8,
            responsive: true,
            restore: false,
            guides: true,
            center: true,
            highlight: false,
            cropBoxMovable: true,
            cropBoxResizable: true,
            toggleDragModeOnDblclick: false,
          });
        }, { once: true });
      };
      reader.readAsDataURL(selectedProfileFile);
    });

    // Profile cropper control functions
    function rotateProfileCropper(degree) {
      if (profileCropper) {
        profileCropper.rotate(degree);
      }
    }

    function resetProfileCropper() {
      if (profileCropper) {
        profileCropper.reset();
      }
    }

    function cropAndSaveProfile() {
      if (!profileCropper) return;

      const canvas = profileCropper.getCroppedCanvas({
        width: 200,
        height: 200,
        imageSmoothingEnabled: true,
        imageSmoothingQuality: 'high',
      });

      const croppedDataUrl = canvas.toDataURL('image/jpeg', 0.9);

      // Update the preview image
      const currentImageContainer = document.getElementById('currentImageContainer');
      currentImageContainer.innerHTML = `
        <img src="${croppedDataUrl}" alt="Cropped Profile Image" class="cropped-preview">
      `;

      // Store cropped image data
      document.getElementById('croppedImageData').value = croppedDataUrl;

      // Hide modal and destroy cropper
      const cropModal = bootstrap.Modal.getInstance(document.getElementById('profileCropModal'));
      cropModal.hide();

      if (profileCropper) {
        profileCropper.destroy();
        profileCropper = null;
      }

      // Hide crop button and clear file input
      document.getElementById('cropImageBtn').style.display = 'none';
      document.getElementById('profileImage').value = '';

      Swal.fire({
        icon: 'success',
        title: 'Image Cropped',
        text: 'Your profile image has been cropped successfully. Click "Save Changes" to update your profile.',
        confirmButtonColor: '#6200ea'
      });
    }

    function confirmLogout(event) {
      event.preventDefault();
      Swal.fire({
        title: 'Are you sure?',
        text: 'You will be logged out of your account',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#6200ea',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, logout'
      }).then((result) => {
        if (result.isConfirmed) {
          window.location.href = '/logout';
        }
      });
    }

    function requestEmailChange() {
      Swal.fire({
        title: 'Change Email Address',
        input: 'email',
        inputLabel: 'Enter your new email address',
        inputPlaceholder: '<EMAIL>',
        showCancelButton: true,
        confirmButtonColor: '#6200ea',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Send Verification',
        inputValidator: (value) => {
          if (!value) {
            return 'Please enter an email address';
          }
          if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            return 'Please enter a valid email address';
          }
        }
      }).then((result) => {
        if (result.isConfirmed) {
          fetch('/profile/change-email', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ newEmail: result.value })
          })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              Swal.fire({
                icon: 'success',
                title: 'Verification Email Sent',
                text: data.message,
                confirmButtonColor: '#6200ea'
              });
            } else {
              Swal.fire({
                icon: 'error',
                title: 'Error',
                text: data.message,
                confirmButtonColor: '#6200ea'
              });
            }
          })
          .catch(error => {
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: 'Failed to send verification email',
              confirmButtonColor: '#6200ea'
            });
          });
        }
      });
    }
  </script>
</body>
</html>
