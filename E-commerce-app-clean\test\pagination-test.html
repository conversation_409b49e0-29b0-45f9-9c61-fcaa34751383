<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pagination Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Pagination Test</h1>
        
        <div class="products-grid-container">
            <div class="row">
                <div class="col-12">
                    <p>Test content for pagination</p>
                </div>
            </div>
        </div>
        
        <div class="pagination-container">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    <li class="page-item">
                        <a class="page-link" href="#" data-pagination-page="1">1</a>
                    </li>
                    <li class="page-item active">
                        <a class="page-link" href="#" data-pagination-page="2">2</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#" data-pagination-page="3">3</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="../public/js/pagination-utils.js"></script>
    <script src="../public/js/shop-pagination.js"></script>
    
    <script>
        console.log('Testing pagination utilities...');
        console.log('PaginationUtils available:', typeof window.PaginationUtils);
        console.log('ShopPagination available:', typeof window.ShopPagination);
        
        // Test pagination click
        document.addEventListener('click', function(e) {
            if (e.target.closest('[data-pagination-page]')) {
                console.log('Pagination link clicked:', e.target.closest('[data-pagination-page]').dataset.paginationPage);
            }
        });
    </script>
</body>
</html>
