{"name": "backend-workouts", "version": "1.0.0", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon server.js"}, "author": "", "type": "module", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^5.1.1", "cookie-parser": "^1.4.7", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^5.1.0", "express-ejs-layouts": "^2.5.1", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "multer": "^1.4.5-lts.2", "nodemailer": "^7.0.2", "nodemon": "^3.1.10", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "pdfkit": "^0.17.1", "sharp": "^0.34.1", "twilio": "^5.6.0"}}