<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Stock Alerts - Admin Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="/css/admin.css">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    body {
      background-color: #f8f9fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
    }

    .main-content {
      margin-left: 250px;
      min-height: 100vh;
      transition: margin-left 0.3s ease;
    }

    @media (max-width: 768px) {
      .main-content {
        margin-left: 0;
      }
    }

    .admin-header {
      background: linear-gradient(135deg, #dc3545 0%, #ffc107 100%);
      color: white;
      padding: 2rem 0;
      margin-bottom: 2rem;
    }

    .alert-card {
      background: white;
      border-radius: 15px;
      padding: 1.5rem;
      margin-bottom: 1rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }

    .alert-card:hover {
      transform: translateY(-2px);
    }

    .critical-alert {
      border-left: 4px solid #dc3545;
    }

    .warning-alert {
      border-left: 4px solid #ffc107;
    }

    .product-image {
      width: 60px;
      height: 60px;
      object-fit: cover;
      border-radius: 8px;
    }

    .stock-badge {
      padding: 0.5rem 1rem;
      border-radius: 25px;
      font-weight: 600;
      font-size: 0.8rem;
      text-transform: uppercase;
    }

    .stock-critical { background-color: #f8d7da; color: #721c24; }
    .stock-warning { background-color: #fff3cd; color: #856404; }

    .action-btn {
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
      margin: 0.2rem;
      transition: all 0.3s ease;
    }

    .btn-restock { background: #28a745; color: white; }
    .btn-restock:hover { background: #218838; color: white; }

    .btn-edit { background: #007bff; color: white; }
    .btn-edit:hover { background: #0056b3; color: white; }

    .btn-back { background: #6c757d; color: white; }
    .btn-back:hover { background: #5a6268; color: white; }

    .summary-card {
      background: white;
      border-radius: 15px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .stat-item {
      text-align: center;
      padding: 1rem;
    }

    .stat-number {
      font-size: 2rem;
      font-weight: bold;
    }

    .stat-label {
      color: #6c757d;
      font-size: 0.9rem;
    }

    .critical-number {
      color: #dc3545;
    }

    .warning-number {
      color: #ffc107;
    }

    .section-header {
      background: white;
      border-radius: 15px;
      padding: 1rem 1.5rem;
      margin-bottom: 1rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .quick-restock {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-top: 1rem;
    }

    .stock-input {
      width: 80px;
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
      .admin-header .col-md-6 {
        text-align: center !important;
        margin-bottom: 1rem;
      }

      .alert-card .col-md-1,
      .alert-card .col-md-2,
      .alert-card .col-md-3,
      .alert-card .col-md-4 {
        margin-bottom: 0.5rem;
        text-align: center;
      }

      .quick-restock {
        justify-content: center;
      }

      .action-btn {
        display: block;
        width: 100%;
        margin-bottom: 0.25rem;
      }
    }

    @media (max-width: 576px) {
      .summary-card {
        padding: 1rem;
      }

      .stat-item {
        padding: 0.5rem;
      }

      .stat-number {
        font-size: 1.5rem;
      }

      .alert-card {
        padding: 1rem;
      }

      .alert-card .row {
        text-align: center;
      }

      .product-image {
        width: 40px;
        height: 40px;
      }
    }
  </style>
</head>
<body>
  <!-- Include Admin Sidebar -->
  <%- include('./partials/sidebar', { page: 'inventory' }) %>

  <div class="main-content">
    <!-- Admin Header -->
    <div class="admin-header">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-md-6">
            <h1><i class="fas fa-exclamation-triangle me-3"></i>Stock Alerts</h1>
            <p class="mb-0">Critical and low stock notifications</p>
          </div>
          <div class="col-md-6 text-end">
            <a href="/admin/inventory" class="action-btn btn-back">
              <i class="fas fa-arrow-left me-2"></i>Back to Inventory
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="container">
    <!-- Summary -->
    <div class="summary-card">
      <div class="row">
        <div class="col-md-6">
          <div class="stat-item">
            <div class="stat-number critical-number"><%= outOfStockProducts.length %></div>
            <div class="stat-label">Out of Stock Products</div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="stat-item">
            <div class="stat-number warning-number"><%= lowStockProducts.length %></div>
            <div class="stat-label">Low Stock Products (≤<%= lowStockThreshold %>)</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Out of Stock Products -->
    <% if (outOfStockProducts && outOfStockProducts.length > 0) { %>
      <div class="section-header">
        <h4 class="mb-0 text-danger">
          <i class="fas fa-times-circle me-2"></i>Out of Stock Products (<%= outOfStockProducts.length %>)
        </h4>
      </div>

      <% outOfStockProducts.forEach(product => { %>
        <div class="alert-card critical-alert">
          <div class="row align-items-center">
            <div class="col-md-1">
              <% if (product.productImage && product.productImage.length > 0) { %>
                <img src="/uploads/product-images/<%= product.productImage[0] %>"
                     alt="<%= product.productName %>" class="product-image">
              <% } else { %>
                <div class="product-image bg-light d-flex align-items-center justify-content-center">
                  <i class="fas fa-image text-muted"></i>
                </div>
              <% } %>
            </div>
            <div class="col-md-4">
              <h6 class="mb-1"><%= product.productName %></h6>
              <p class="text-muted mb-1">
                <% if (product.category) { %>
                  <%= product.category.name %>
                <% } else { %>
                  No Category
                <% } %>
              </p>
              <small class="text-muted">₹<%= (product.salePrice || product.regularPrice || 0).toFixed(2) %></small>
            </div>
            <div class="col-md-2">
              <span class="stock-badge stock-critical">
                <i class="fas fa-times-circle me-1"></i>Out of Stock
              </span>
            </div>
            <div class="col-md-3">
              <div class="quick-restock">
                <span class="text-muted">Restock:</span>
                <input type="number" class="form-control stock-input"
                       id="restock-<%= product._id %>" value="50" min="1">
                <button class="btn btn-sm btn-success" onclick="quickRestock('<%= product._id %>')">
                  <i class="fas fa-plus"></i>
                </button>
              </div>
            </div>
            <div class="col-md-2 text-end">
              <a href="/admin/edit-product/<%= product._id %>" class="action-btn btn-edit">
                <i class="fas fa-edit me-1"></i>Edit
              </a>
            </div>
          </div>
        </div>
      <% }); %>
    <% } %>

    <!-- Low Stock Products -->
    <% if (lowStockProducts && lowStockProducts.length > 0) { %>
      <div class="section-header">
        <h4 class="mb-0 text-warning">
          <i class="fas fa-exclamation-triangle me-2"></i>Low Stock Products (<%= lowStockProducts.length %>)
        </h4>
      </div>

      <% lowStockProducts.forEach(product => { %>
        <% if (product.quantity > 0) { %>
          <div class="alert-card warning-alert">
            <div class="row align-items-center">
              <div class="col-md-1">
                <% if (product.productImage && product.productImage.length > 0) { %>
                  <img src="/uploads/product-images/<%= product.productImage[0] %>"
                       alt="<%= product.productName %>" class="product-image">
                <% } else { %>
                  <div class="product-image bg-light d-flex align-items-center justify-content-center">
                    <i class="fas fa-image text-muted"></i>
                  </div>
                <% } %>
              </div>
              <div class="col-md-4">
                <h6 class="mb-1"><%= product.productName %></h6>
                <p class="text-muted mb-1">
                  <% if (product.category) { %>
                    <%= product.category.name %>
                  <% } else { %>
                    No Category
                  <% } %>
                </p>
                <small class="text-muted">₹<%= product.salePrice ? product.salePrice.toFixed(2) : 'N/A' %></small>
              </div>
              <div class="col-md-2">
                <span class="stock-badge stock-warning">
                  <i class="fas fa-exclamation-triangle me-1"></i><%= product.quantity %> left
                </span>
              </div>
              <div class="col-md-3">
                <div class="quick-restock">
                  <span class="text-muted">Add:</span>
                  <input type="number" class="form-control stock-input"
                         id="restock-<%= product._id %>" value="<%= 50 - product.quantity %>" min="1">
                  <button class="btn btn-sm btn-success" onclick="quickRestock('<%= product._id %>')">
                    <i class="fas fa-plus"></i>
                  </button>
                </div>
              </div>
              <div class="col-md-2 text-end">
                <a href="/admin/edit-product/<%= product._id %>" class="action-btn btn-edit">
                  <i class="fas fa-edit me-1"></i>Edit
                </a>
              </div>
            </div>
          </div>
        <% } %>
      <% }); %>
    <% } %>

    <!-- No Alerts -->
    <% if ((!outOfStockProducts || outOfStockProducts.length === 0) && (!lowStockProducts || lowStockProducts.length === 0)) { %>
      <div class="alert-card text-center">
        <div class="py-5">
          <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
          <h3>All Good!</h3>
          <p class="text-muted">No stock alerts at the moment. All products have sufficient stock.</p>
          <a href="/admin/inventory" class="btn btn-primary">
            <i class="fas fa-boxes me-2"></i>View Inventory
          </a>
        </div>
      </div>
    <% } %>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Quick restock function
    async function quickRestock(productId) {
      const quantityInput = document.getElementById(`restock-${productId}`);
      const quantity = parseInt(quantityInput.value);

      if (!quantity || quantity <= 0) {
        Swal.fire({
          icon: 'warning',
          title: 'Invalid Quantity',
          text: 'Please enter a valid quantity.',
          confirmButtonColor: '#28a745'
        });
        return;
      }

      try {
        const response = await fetch(`/admin/inventory/${productId}/stock`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ quantity, action: 'add' })
        });

        const result = await response.json();

        if (result.success) {
          Swal.fire({
            icon: 'success',
            title: 'Stock Updated',
            text: result.message,
            confirmButtonColor: '#28a745'
          }).then(() => {
            location.reload();
          });
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Update Failed',
            text: result.message,
            confirmButtonColor: '#dc3545'
          });
        }
      } catch (error) {
        console.error('Error updating stock:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to update stock. Please try again.',
          confirmButtonColor: '#dc3545'
        });
      }
    }

    // Auto-refresh every 5 minutes
    setTimeout(() => {
      location.reload();
    }, 5 * 60 * 1000);
  </script>
    </div> <!-- End container -->
  </div> <!-- End main-content -->
</body>
</html>
