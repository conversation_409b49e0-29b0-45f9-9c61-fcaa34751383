<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Return Requests - Admin Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="/css/admin.css">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    body {
      background-color: #f8f9fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
    }

    .admin-header {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
      padding: 2rem 0;
      margin-bottom: 2rem;
    }

    .request-card {
      background: white;
      border-radius: 15px;
      padding: 1.5rem;
      margin-bottom: 1rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
      border-left: 4px solid #ffc107;
    }

    .request-card:hover {
      transform: translateY(-2px);
    }

    .urgent-request {
      border-left-color: #dc3545;
    }

    .status-badge {
      padding: 0.5rem 1rem;
      border-radius: 25px;
      font-weight: 600;
      font-size: 0.8rem;
      text-transform: uppercase;
      background-color: #fff3cd;
      color: #856404;
    }

    .action-btn {
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
      margin: 0.2rem;
      transition: all 0.3s ease;
    }

    .btn-view { background: #17a2b8; color: white; }
    .btn-view:hover { background: #138496; color: white; }

    .btn-approve { background: #28a745; color: white; }
    .btn-approve:hover { background: #218838; color: white; }

    .btn-reject { background: #dc3545; color: white; }
    .btn-reject:hover { background: #c82333; color: white; }

    .btn-back { background: #6c757d; color: white; }
    .btn-back:hover { background: #5a6268; color: white; }

    .return-reason {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 1rem;
      margin-top: 1rem;
      border-left: 3px solid #ffc107;
    }

    .customer-info {
      background: #e9ecef;
      border-radius: 8px;
      padding: 1rem;
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 2rem;
    }

    .stats-card {
      background: white;
      border-radius: 15px;
      padding: 1.5rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-bottom: 1rem;
      transition: transform 0.3s ease;
      border-left: 4px solid transparent;
    }

    .stats-card:hover {
      transform: translateY(-5px);
    }

    .stats-card.pending {
      border-left-color: #ffc107;
    }

    .stats-card.urgent {
      border-left-color: #dc3545;
    }

    .stats-card.value {
      border-left-color: #28a745;
    }

    .stats-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      color: white;
      margin: 0 auto 1rem;
    }

    .stat-number {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 0.5rem;
    }

    .stat-label {
      color: #6c757d;
      font-size: 0.9rem;
      font-weight: 500;
    }

    .search-filter-section {
      background: white;
      border-radius: 15px;
      padding: 2rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
      .admin-header .col-md-6 {
        text-align: center !important;
        margin-bottom: 1rem;
      }

      .request-card .col-md-2,
      .request-card .col-md-3,
      .request-card .col-md-1 {
        margin-bottom: 0.5rem;
        text-align: center;
      }

      .action-btn {
        display: block;
        width: 100%;
        margin-bottom: 0.25rem;
      }

      .customer-info {
        margin-bottom: 0.5rem;
      }
    }

    @media (max-width: 576px) {
      .stats-summary {
        padding: 1rem;
      }

      .stat-item {
        padding: 0.5rem;
      }

      .stat-number {
        font-size: 1.5rem;
      }

      .request-card {
        padding: 1rem;
      }

      .request-card .row {
        text-align: center;
      }

      .action-btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
      }
    }
  </style>
</head>
<body>
  <!-- Include Admin Sidebar -->
  <%- include('./partials/sidebar', { page: 'return-requests' }) %>

  <div class="main-content">
    <!-- Admin Header -->
    <div class="admin-header">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-md-6">
            <h1><i class="fas fa-undo me-3"></i>Return Requests</h1>
            <p class="mb-0">Manage customer return requests</p>
          </div>
          <div class="col-md-6 text-end">
            <a href="/admin/orders" class="action-btn btn-back">
              <i class="fas fa-arrow-left me-2"></i>Back to Orders
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="container">
    <!-- Statistics Summary -->
    <div class="row mb-4">
      <div class="col-md-4 col-sm-6 col-12">
        <div class="stats-card pending text-center">
          <div class="stats-icon mx-auto" style="background: #ffc107;">
            <i class="fas fa-clock"></i>
          </div>
          <div class="stat-number text-warning"><%= totalRequests %></div>
          <div class="stat-label">Pending Requests</div>
        </div>
      </div>
      <div class="col-md-4 col-sm-6 col-12">
        <div class="stats-card urgent text-center">
          <div class="stats-icon mx-auto" style="background: #dc3545;">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="stat-number text-danger">
            <%
              let urgentCount = 0;
              if (returnRequests) {
                urgentCount = returnRequests.filter(req => {
                  const daysSinceRequest = Math.floor((Date.now() - new Date(req.returnRequestedAt).getTime()) / (1000 * 60 * 60 * 24));
                  return daysSinceRequest >= 3;
                }).length;
              }
            %>
            <%= urgentCount %>
          </div>
          <div class="stat-label">Urgent (3+ days)</div>
        </div>
      </div>
      <div class="col-md-4 col-sm-12 col-12">
        <div class="stats-card value text-center">
          <div class="stats-icon mx-auto" style="background: #28a745;">
            <i class="fas fa-rupee-sign"></i>
          </div>
          <div class="stat-number text-success">
            <%
              let totalAmount = 0;
              if (returnRequests) {
                totalAmount = returnRequests.reduce((sum, req) => sum + req.totalAmount, 0);
              }
            %>
            ₹<%= (totalAmount || 0).toFixed(0) %>
          </div>
          <div class="stat-label">Total Value</div>
        </div>
      </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="search-filter-section">
      <form method="GET" action="/admin/return-requests" class="row g-3">
        <div class="col-md-4">
          <label class="form-label">Search Orders</label>
          <input type="text" class="form-control" name="search" value="<%= typeof search !== 'undefined' ? search : '' %>"
                 placeholder="Order number or customer name...">
        </div>
        <div class="col-md-3">
          <label class="form-label">Status</label>
          <select class="form-select" name="status">
            <option value="">All Status</option>
            <option value="urgent" <%= (typeof status !== 'undefined' && status === 'urgent') ? 'selected' : '' %>>Urgent (3+ days)</option>
            <option value="recent" <%= (typeof status !== 'undefined' && status === 'recent') ? 'selected' : '' %>>Recent (< 3 days)</option>
          </select>
        </div>
        <div class="col-md-3">
          <label class="form-label">Sort By</label>
          <select class="form-select" name="sortBy">
            <option value="returnRequestedAt" <%= (typeof sortBy !== 'undefined' && sortBy === 'returnRequestedAt') ? 'selected' : '' %>>Request Date</option>
            <option value="totalAmount" <%= (typeof sortBy !== 'undefined' && sortBy === 'totalAmount') ? 'selected' : '' %>>Amount</option>
            <option value="orderDate" <%= (typeof sortBy !== 'undefined' && sortBy === 'orderDate') ? 'selected' : '' %>>Order Date</option>
          </select>
        </div>
        <div class="col-md-2">
          <label class="form-label">&nbsp;</label>
          <div class="d-flex">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-search me-1"></i>Search
            </button>
            <a href="/admin/return-requests" class="btn btn-outline-secondary ms-2">
              <i class="fas fa-times me-1"></i>Clear
            </a>
          </div>
        </div>
      </form>
    </div>

    <!-- Return Requests List -->
    <% if (returnRequests && returnRequests.length > 0) { %>
      <% returnRequests.forEach(request => { %>
        <%
          const daysSinceRequest = Math.floor((Date.now() - new Date(request.returnRequestedAt).getTime()) / (1000 * 60 * 60 * 24));
          const isUrgent = daysSinceRequest >= 3;
        %>
        <div class="request-card <%= isUrgent ? 'urgent-request' : '' %>">
          <div class="row align-items-center">
            <div class="col-md-2">
              <h6 class="mb-1">Order #<%= request.orderNumber %></h6>
              <small class="text-muted">
                <%= new Date(request.orderDate).toLocaleDateString('en-IN', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                }) %>
              </small>
              <% if (isUrgent) { %>
                <div class="mt-1">
                  <span class="badge bg-danger">Urgent</span>
                </div>
              <% } %>
            </div>

            <div class="col-md-2">
              <div class="customer-info">
                <h6 class="mb-1"><%= request.user.name %></h6>
                <small class="text-muted"><%= request.user.email %></small>
                <% if (request.user.phone) { %>
                  <br><small class="text-muted"><%= request.user.phone %></small>
                <% } %>
              </div>
            </div>

            <div class="col-md-2">
              <h6 class="mb-1">Amount</h6>
              <p class="mb-0 fw-bold">₹<%= (request.totalAmount || 0).toFixed(2) %></p>
              <small class="text-muted"><%= request.items.length %> item(s)</small>
            </div>

            <div class="col-md-2">
              <h6 class="mb-1">Requested</h6>
              <p class="mb-0">
                <%= new Date(request.returnRequestedAt).toLocaleDateString('en-IN', {
                  month: 'short',
                  day: 'numeric'
                }) %>
              </p>
              <small class="text-muted"><%= daysSinceRequest %> day(s) ago</small>
            </div>

            <div class="col-md-1">
              <span class="status-badge">Pending</span>
            </div>

            <div class="col-md-3 text-end">
              <a href="/admin/orders/<%= request._id %>" class="action-btn btn-view">
                <i class="fas fa-eye me-1"></i>View
              </a>
              <button class="action-btn btn-approve" onclick="handleReturn('<%= request._id %>', '<%= request.orderNumber %>', 'approve')">
                <i class="fas fa-check me-1"></i>Approve
              </button>
              <button class="action-btn btn-reject" onclick="handleReturn('<%= request._id %>', '<%= request.orderNumber %>', 'reject')">
                <i class="fas fa-times me-1"></i>Reject
              </button>
            </div>
          </div>

          <!-- Return Reason -->
          <% if (request.returnReason) { %>
            <div class="return-reason">
              <h6 class="mb-2"><i class="fas fa-comment me-2"></i>Return Reason:</h6>
              <p class="mb-0"><%= request.returnReason %></p>
            </div>
          <% } %>
        </div>
      <% }); %>

      <!-- Pagination -->
      <% if (totalPages > 1) { %>
        <div class="pagination-container">
          <nav aria-label="Return requests pagination">
            <ul class="pagination">
              <% if (currentPage > 1) { %>
                <li class="page-item">
                  <a class="page-link" href="?page=<%= currentPage - 1 %><% if (typeof search !== 'undefined' && search) { %>&search=<%= search %><% } %><% if (typeof status !== 'undefined' && status) { %>&status=<%= status %><% } %><% if (typeof sortBy !== 'undefined' && sortBy) { %>&sortBy=<%= sortBy %><% } %>">
                    <i class="fas fa-chevron-left"></i>
                  </a>
                </li>
              <% } %>

              <% for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) { %>
                <li class="page-item <%= i === currentPage ? 'active' : '' %>">
                  <a class="page-link" href="?page=<%= i %><% if (typeof search !== 'undefined' && search) { %>&search=<%= search %><% } %><% if (typeof status !== 'undefined' && status) { %>&status=<%= status %><% } %><% if (typeof sortBy !== 'undefined' && sortBy) { %>&sortBy=<%= sortBy %><% } %>">
                    <%= i %>
                  </a>
                </li>
              <% } %>

              <% if (currentPage < totalPages) { %>
                <li class="page-item">
                  <a class="page-link" href="?page=<%= currentPage + 1 %><% if (typeof search !== 'undefined' && search) { %>&search=<%= search %><% } %><% if (typeof status !== 'undefined' && status) { %>&status=<%= status %><% } %><% if (typeof sortBy !== 'undefined' && sortBy) { %>&sortBy=<%= sortBy %><% } %>">
                    <i class="fas fa-chevron-right"></i>
                  </a>
                </li>
              <% } %>
            </ul>
          </nav>
        </div>
      <% } %>

    <% } else { %>
      <!-- Empty State -->
      <div class="request-card text-center">
        <div class="py-5">
          <i class="fas fa-undo fa-3x text-muted mb-3"></i>
          <h3>No Pending Return Requests</h3>
          <p class="text-muted">All return requests have been processed.</p>
          <a href="/admin/orders" class="action-btn btn-view">
            <i class="fas fa-shopping-cart me-2"></i>View All Orders
          </a>
        </div>
      </div>
    <% } %>
  </div>

  <!-- Return Action Modal -->
  <div class="modal fade" id="returnActionModal" tabindex="-1" aria-labelledby="returnActionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="returnActionModalLabel">Process Return Request</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <p><span id="actionText">Approve</span> return request for order <strong id="modalOrderNumber"></strong>?</p>
          <div class="mb-3">
            <label for="adminNotes" class="form-label">Admin Notes (optional):</label>
            <textarea class="form-control" id="adminNotes" rows="3" placeholder="Add notes for the customer..."></textarea>
          </div>
          <div id="approveWarning" class="alert alert-info" style="display: none;">
            <i class="fas fa-info-circle me-2"></i>
            Approving this return will automatically refund the amount to the customer's wallet and restore product stock.
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn" id="confirmActionBtn">Process</button>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    let currentOrderId = null;
    let currentAction = null;

    // Handle return request
    function handleReturn(orderId, orderNumber, action) {
      currentOrderId = orderId;
      currentAction = action;

      document.getElementById('modalOrderNumber').textContent = orderNumber;
      document.getElementById('actionText').textContent = action === 'approve' ? 'Approve' : 'Reject';
      document.getElementById('adminNotes').value = '';

      const confirmBtn = document.getElementById('confirmActionBtn');
      const approveWarning = document.getElementById('approveWarning');

      if (action === 'approve') {
        confirmBtn.className = 'btn btn-success';
        confirmBtn.textContent = 'Approve Return';
        approveWarning.style.display = 'block';
      } else {
        confirmBtn.className = 'btn btn-danger';
        confirmBtn.textContent = 'Reject Return';
        approveWarning.style.display = 'none';
      }

      new bootstrap.Modal(document.getElementById('returnActionModal')).show();
    }

    // Confirm return action
    document.getElementById('confirmActionBtn').addEventListener('click', async function() {
      if (!currentOrderId || !currentAction) return;

      const adminNotes = document.getElementById('adminNotes').value.trim();

      try {
        const response = await fetch(`/admin/orders/${currentOrderId}/return`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: currentAction,
            adminNotes: adminNotes
          })
        });

        const result = await response.json();

        if (result.success) {
          let message = result.message;
          if (currentAction === 'approve' && result.refundAmount) {
            message += ` Refund amount: ₹${result.refundAmount.toFixed(2)}`;
          }

          Swal.fire({
            icon: 'success',
            title: currentAction === 'approve' ? 'Return Approved' : 'Return Rejected',
            text: message,
            confirmButtonColor: '#28a745'
          }).then(() => {
            location.reload();
          });
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Action Failed',
            text: result.message,
            confirmButtonColor: '#dc3545'
          });
        }
      } catch (error) {
        console.error('Error processing return:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to process return request. Please try again.',
          confirmButtonColor: '#dc3545'
        });
      }

      bootstrap.Modal.getInstance(document.getElementById('returnActionModal')).hide();
    });
  </script>
    </div> <!-- End container -->
  </div> <!-- End main-content -->
</body>
</html>
