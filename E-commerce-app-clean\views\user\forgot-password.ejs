<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Forgot Password | Luxe Scents</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .forgot-container {
      max-width: 450px;
      width: 100%;
      margin: 2rem;
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    .forgot-header {
      background: linear-gradient(135deg, #6200ea, #bb86fc);
      color: white;
      padding: 2rem;
      text-align: center;
    }
    .forgot-header h2 {
      margin-bottom: 0.5rem;
      font-weight: 600;
    }
    .forgot-header p {
      margin-bottom: 0;
      opacity: 0.9;
    }
    .forgot-body {
      padding: 2rem;
    }
    .form-control {
      border-radius: 10px;
      border: 2px solid #e9ecef;
      padding: 0.75rem 1rem;
      transition: all 0.3s;
      font-size: 1rem;
    }
    .form-control:focus {
      border-color: #6200ea;
      box-shadow: 0 0 0 0.2rem rgba(98, 0, 234, 0.25);
    }
    .btn-primary {
      background-color: #6200ea;
      border-color: #6200ea;
      border-radius: 25px;
      padding: 0.75rem 2rem;
      font-weight: 500;
      transition: all 0.3s;
      width: 100%;
      font-size: 1rem;
    }
    .btn-primary:hover {
      background-color: #5300d1;
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(98, 0, 234, 0.3);
    }
    .btn-outline-secondary {
      border-radius: 25px;
      padding: 0.75rem 2rem;
      font-weight: 500;
      transition: all 0.3s;
      width: 100%;
      font-size: 1rem;
    }
    .btn-outline-secondary:hover {
      transform: translateY(-2px);
    }
    .alert {
      border-radius: 10px;
      border: none;
      margin-bottom: 1.5rem;
    }
    .alert-success {
      background-color: #d4edda;
      color: #155724;
    }
    .alert-danger {
      background-color: #f8d7da;
      color: #721c24;
    }
    .info-section {
      background-color: #f8f9fa;
      padding: 1.5rem;
      border-radius: 10px;
      margin-bottom: 1.5rem;
      text-align: center;
    }
    .info-section i {
      font-size: 3rem;
      color: #6200ea;
      margin-bottom: 1rem;
    }
    .info-section h5 {
      color: #333;
      margin-bottom: 0.5rem;
    }
    .info-section p {
      color: #666;
      margin-bottom: 0;
      font-size: 0.9rem;
    }
    .back-link {
      text-align: center;
      margin-top: 1.5rem;
    }
    .back-link a {
      color: #6200ea;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s;
    }
    .back-link a:hover {
      color: #5300d1;
    }
    @media (max-width: 768px) {
      .forgot-container {
        margin: 1rem;
      }
      .forgot-header {
        padding: 1.5rem;
      }
      .forgot-body {
        padding: 1.5rem;
      }
    }
  </style>
</head>
<body>
  <div class="forgot-container">
    <!-- Header -->
    <div class="forgot-header">
      <h2><i class="fas fa-key me-2"></i>Forgot Password</h2>
      <p>Enter your email to reset your password</p>
    </div>

    <!-- Body -->
    <div class="forgot-body">
      <!-- Success/Error Messages with SweetAlert -->
      <% if (message) { %>
        <script>
          Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: '<%= message %>',
            confirmButtonColor: '#6200ea'
          });
        </script>
      <% } %>
      <% if (error) { %>
        <script>
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: '<%= error %>',
            confirmButtonColor: '#6200ea'
          });
        </script>
      <% } %>

      <!-- Info Section -->
      <div class="info-section">
        <i class="fas fa-envelope"></i>
        <h5>Password Reset</h5>
        <p>Enter your email address and we'll send you a link to reset your password. The link will expire in 15 minutes for security.</p>
      </div>

      <!-- Forgot Password Form -->
      <form action="/forgot-password" method="POST" id="forgotPasswordForm">
        <div class="mb-3">
          <label for="email" class="form-label">Email Address</label>
          <div class="input-group">
            <span class="input-group-text" style="border-radius: 10px 0 0 10px; border: 2px solid #e9ecef; border-right: none;">
              <i class="fas fa-envelope text-muted"></i>
            </span>
            <input type="email" class="form-control" id="email" name="email" required
                   placeholder="Enter your email address"
                   style="border-radius: 0 10px 10px 0; border-left: none;">
          </div>
        </div>

        <div class="d-grid gap-2">
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-paper-plane me-2"></i>Send Reset Link
          </button>
          <a href="/login" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Login
          </a>
        </div>
      </form>

      <!-- Additional Links -->
      <div class="back-link">
        <p class="text-muted mb-2">Remember your password?</p>
        <a href="/login">
          <i class="fas fa-sign-in-alt me-1"></i>Sign In
        </a>
        <span class="mx-2">|</span>
        <a href="/register">
          <i class="fas fa-user-plus me-1"></i>Create Account
        </a>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Enhanced form validation with underscore check
    document.getElementById('forgotPasswordForm').addEventListener('submit', function(e) {
      const email = document.getElementById('email').value.trim();

      if (!email) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Email Required',
          text: 'Please enter your email address',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Check for underscores in email (not allowed in forgot password)
      if (email.includes('_')) {
        e.preventDefault();
        Swal.fire({
          icon: 'error',
          title: 'Invalid Email Format',
          text: 'Email addresses with underscores are not supported for password recovery. Please contact support if you registered with an underscore email.',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Basic email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Invalid Email Format',
          text: 'Please enter a valid email address',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Email length validation
      if (email.length > 254) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Email Too Long',
          text: 'Email address is too long (maximum 254 characters)',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Check for consecutive dots
      if (/\.{2,}/.test(email)) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Invalid Email Format',
          text: 'Email cannot contain consecutive dots',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Check for dots at start or end of local part
      if (/^\.|\.$|@\.|\.\@/.test(email)) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Invalid Email Format',
          text: 'Email cannot start or end with dots around @ symbol',
          confirmButtonColor: '#6200ea'
        });
        return;
      }
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
      const alerts = document.querySelectorAll('.alert');
      alerts.forEach(function(alert) {
        const bsAlert = new bootstrap.Alert(alert);
        bsAlert.close();
      });
    }, 5000);
  </script>
</body>
</html>
