import { Order } from "../../model/orderModel.js";
import { Product } from "../../model/productModel.js";
import { catchAsyncError } from "../../middlewares/catchAsync.js";


export const getOrderDetails = catchAsyncError(async (req, res, next) => {
  try {
    const { orderId } = req.params;


    const order = await Order.findOne({
      _id: orderId,
      user: req.user._id
    }).populate('items.product');

    if (!order) {
      return res.redirect('/?error=Order not found');
    }

    const canBeCancelled = order.canBeCancelled();
    const canRequestReturn = order.canRequestReturn();

    const orderObj = order.toObject();
    orderObj.canBeCancelled = canBeCancelled;
    orderObj.canRequestReturn = canRequestReturn;

    res.render("user/order-details", {
      order: orderObj,
      message: req.query.message || null,
      error: req.query.error || null
    });

  } catch (error) {
    console.error("Error loading order details:", error);
    return res.redirect('/?error=Failed to load order details');
  }
});

