import {Product} from "../model/productModel.js"
import {Category }from "../model/categoryModel.js";
import mongoose from "mongoose";
import fs from "fs";
import path from "path";
import sharp from "sharp";
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const getProductAddPage = async (req, res) => {
  try {
    const cat= await Category.find({ isListed: true });
    const error = req.query.error || null;
    res.render("product-add", { cat,error });
  } catch (error) {
    res.redirect("/pageNotFound");
  }
};

const addProducts = async (req, res) => {
  try {
    const productData = req.body;

    // Check if product already exists
    const productExists = await Product.findOne({ productName: productData.productName });
    if (productExists) {
      return res.redirect("/admin/add-products?error=Product+already+exists");
    }

    // Validate that we have exactly 3 images
    if (!req.files || req.files.length < 3) {
      return res.redirect("/admin/add-products?error=At+least+3+images+are+required");
    }

    // Process images
    const images = [];
    if (req.files && req.files.length > 0) {
      req.files.forEach(file => {
        images.push(file.filename);
      });
    }

    // Find category
    const categoryDoc = await Category.findOne({ name: productData.category });
    if (!categoryDoc) {
      return res.redirect("/admin/add-products?error=Invalid+category+name");
    }

    // Set status based on quantity
    const status = productData.quantity > 0 ? "Available" : "Out of Stock";

    // Create and save product
    const product = new Product({
      productName: productData.productName,
      description: productData.description,
      category: categoryDoc._id,
      price: productData.price,
      quantity: productData.quantity,
      productImage: images,
      status,
    });

    await product.save();
    console.log("Product saved successfully with images:", images);

    return res.redirect("/admin/products");

  } catch (error) {
    console.error("Error saving product:", error);
    return res.redirect("/admin/pageerror");
  }
};






const getProductList = async (req, res) => {
  try {
    // Get products, including those that might be soft-deleted if needed
    const products = await Product.find()
      .populate("category")
      .sort({ createdAt: -1 });

    // Get error and success messages from query params
    const error = req.query.error || null;
    const message = req.query.message || null;

    res.render("product", {
      products,
      error,
      message
    });
  } catch (error) {
    console.error("Error fetching products:", error);
    res.redirect("/admin/dashboard?error=Failed+to+load+products");
  }
};
const deleteProduct = async (req, res) => {
  try {
    const productId = req.params.id;

    // Check if product exists
    const product = await Product.findById(productId);
    if (!product) {
      return res.redirect("/admin/products?error=Product+not+found");
    }

    // Block the product instead of soft deleting it
    product.isBlocked = true;
    await product.save();
    console.log(`Product ${productId} blocked successfully`);

    return res.redirect("/admin/products?message=Product+has+been+blocked+and+hidden+from+users");
  } catch (error) {
    console.error("Error blocking product:", error);
    return res.redirect("/admin/products?error=Failed+to+block+product");
  }
};

const toggleBlockProduct = async (req, res) => {
  try {
    const productId = req.params.id;
    const product = await Product.findById(productId);

    if (!product) {
      return res.redirect("/admin/products?error=Product+not+found");
    }

    // Toggle the blocked status
    product.isBlocked = !product.isBlocked;
    await product.save();

    const statusMessage = product.isBlocked
      ? "Product has been blocked"
      : "Product has been unblocked";

    return res.redirect(`/admin/products?message=${statusMessage}`);
  } catch (error) {
    console.error("Error toggling product block status:", error);
    return res.redirect("/admin/products?error=Failed+to+update+product+status");
  }
};

const getEditProductPage = async (req, res) => {
  try {
    const productId = req.params.id;
    const product = await Product.findById(productId).populate("category");
    const categories = await Category.find({ isListed: true });

    if (!product) {
      return res.redirect("/admin/products?error=Product+not+found");
    }

    const error = req.query.error || null;
    res.render("edit-product", { product, categories, error });
  } catch (error) {
    console.error("Error fetching product for edit:", error);
    return res.redirect("/admin/pageerror");
  }
};

const editProduct = async (req, res) => {
  try {
    console.log('Edit product function called');
    const productId = req.params.id;
    const productData = req.body;
    console.log('Product ID:', productId);
    console.log('Request Body:', req.body);
    console.log('Request Files:', req.files);

    // Validate productId
    if (!mongoose.Types.ObjectId.isValid(productId)) {
      return res.redirect(`/admin/edit-product/${productId}?error=Invalid+product+ID`);
    }

    // Fetch product
    const product = await Product.findById(productId);
    if (!product) {
      return res.redirect("/admin/products?error=Product+not+found");
    }

    // Validate category input
    if (!productData.category || typeof productData.category !== 'string' || productData.category.trim() === '') {
      return res.redirect(`/admin/edit-product/${productId}?error=Category+is+required`);
    }

    // Get existing images
    const existingImages = Array.isArray(product.productImage) ? [...product.productImage] : [];
    console.log("Existing images:", existingImages);

    // Process new images
    const imageFields = ['image1', 'image2', 'image3', 'image4'];
    const updatedImages = [...existingImages]; // Start with existing images

    for (let i = 0; i < imageFields.length; i++) {
      const fieldName = imageFields[i];
      const files = req.files && req.files[fieldName];
      const file = files && files[0];

      if (file) {
        console.log(`Processing new image for ${fieldName}:`, file.filename);

        // If we're replacing an existing image at this position
        if (i < existingImages.length) {
          // Try to delete the old image
          try {
            const oldImagePath = path.join(__dirname, "../../public/uploads/product-images", existingImages[i]);
            await fs.promises.unlink(oldImagePath).catch(err => {
              console.warn(`Failed to delete old image ${oldImagePath}: ${err.message}`);
            });
          } catch (err) {
            console.warn(`Error deleting old image: ${err.message}`);
          }

          // Replace with new image
          updatedImages[i] = file.filename;
        } else {
          // Add new image
          updatedImages.push(file.filename);
        }
      }
    }

    // Ensure we have at least 3 images
    if (updatedImages.length < 3) {
      return res.redirect(`/admin/edit-product/${productId}?error=Product+must+have+at+least+3+images`);
    }

    // Find category by name
    const category = await Category.findOne({ name: productData.category.trim() });
    if (!category) {
      return res.redirect(`/admin/edit-product/${productId}?error=Invalid+category+name`);
    }

    // Update product fields with validation
    product.productName = productData.productName?.trim() || product.productName;
    product.description = productData.description?.trim() || product.description;
    product.category = category._id;
    product.price = parseFloat(productData.price);
    product.quantity = parseInt(productData.quantity);
    product.productImage = updatedImages;

    if (isNaN(product.price) || product.price < 0) {
      return res.redirect(`/admin/edit-product/${productId}?error=Invalid+price`);
    }
    if (isNaN(product.quantity) || product.quantity < 0) {
      return res.redirect(`/admin/edit-product/${productId}?error=Invalid+quantity`);
    }

    product.status = product.quantity > 0 ? "Available" : "Out of Stock";

    console.log("Saving product with images:", updatedImages);
    await product.save();
    return res.redirect("/admin/products?message=Product+updated+successfully");
  } catch (error) {
    console.error("Error updating product:", {
      message: error.message,
      stack: error.stack,
      productId: req.params.id,
      body: req.body,
      files: req.files
    });
    return res.redirect(`/admin/edit-product/${req.params.id}?error=Update+failed: ${encodeURIComponent(error.message)}`);
  }
};


export {
  getProductAddPage,
  addProducts,
  getProductList,
  deleteProduct,
  toggleBlockProduct,
  getEditProductPage,
  editProduct,
};