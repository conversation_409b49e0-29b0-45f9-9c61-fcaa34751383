<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product List</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/admin.css">
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <style>
        .product-img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
        }
        .status-available {
            color: #198754;
        }
        .status-out-of-stock {
            color: #dc3545;
        }
        .status-discontinued {
            color: #6c757d;
        }
        .action-btns {
            white-space: nowrap;
        }
        /* Pagination styles */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }
        .pagination-btn {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #e0e0e0;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }
        .pagination-btn:hover:not(.disabled) {
            background-color: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }
        .pagination-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .pagination-btn.active {
            background-color: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }

        /* Search and Filter Styles */
        .search-filter-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .input-group .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        .search-loading {
            position: relative;
        }

        .search-loading::after {
            content: '';
            position: absolute;
            right: 40px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #0d6efd;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: translateY(-50%) rotate(0deg); }
            100% { transform: translateY(-50%) rotate(360deg); }
        }

        .search-info-fade {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .filter-active {
            background-color: #e7f3ff !important;
            border-color: #0d6efd !important;
        }

        .clear-search-btn {
            transition: all 0.2s ease;
        }

        .clear-search-btn:hover {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Include Admin Sidebar -->
    <%- include('partials/sidebar', { page: 'products' }) %>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid mt-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Product Management</h2>
                <div class="d-flex gap-2">
                    <a href="/admin/deleted-products" class="btn btn-outline-secondary">
                        <i class="bi bi-trash"></i> Deleted Products
                    </a>
                    <a href="/admin/add-products" class="btn btn-primary">
                        <i class="bi bi-plus-lg"></i> Add Product
                    </a>
                </div>
            </div>

            <!-- Search and Filter Section -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="searchInput" class="form-label">Search Products</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-search"></i>
                                </span>
                                <input type="text"
                                       id="searchInput"
                                       class="form-control"
                                       placeholder="Search by product name..."
                                       value="<%= search || '' %>">
                                <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                    <i class="bi bi-x-lg"></i>
                                </button>
                            </div>
                            <div id="searchError" class="text-danger small mt-1" style="display: none;"></div>
                        </div>
                        <div class="col-md-4">
                            <label for="categoryFilter" class="form-label">Filter by Category</label>
                            <select id="categoryFilter" class="form-select">
                                <option value="">All Categories</option>
                                <% if (typeof categories !== 'undefined' && categories.length > 0) { %>
                                    <% categories.forEach(cat => { %>
                                        <option value="<%= cat.name %>" <%= (category && category === cat.name) ? 'selected' : '' %>>
                                            <%= cat.name %>
                                        </option>
                                    <% }); %>
                                <% } %>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" id="resetFilters" class="btn btn-outline-secondary" disabled>
                                    <i class="bi bi-arrow-clockwise"></i> Reset
                                </button>
                            </div>

                    </div>

                    <!-- Search Results Info -->
                    <div id="searchInfo" class="mt-3" style="display: none;">
                        <div class="alert alert-info mb-0">
                            <i class="bi bi-info-circle"></i>
                            <span id="searchInfoText"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error and success messages will be handled by SweetAlert -->
            <% if (error) { %>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: '<%= error %>',
                            confirmButtonColor: '#dc3545',
                            confirmButtonText: 'OK'
                        });
                    });
                </script>
            <% } %>

            <% if (typeof message !== 'undefined' && message) { %>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: '<%= message %>',
                            confirmButtonColor: '#28a745',
                            confirmButtonText: 'OK',
                            timer: 3000,
                            timerProgressBar: true
                        });
                    });
                </script>
            <% } %>

            <div class="card">
                <div class="card-body">
                    <!-- Loading Indicator -->
                    <div id="tableLoading" class="text-center py-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="mt-2">Searching products...</div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Image</th>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Price</th>
                                    <th>Quantity</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="productTableBody">
                                <% if (products.length === 0) { %>
                                    <tr>
                                        <td colspan="7" style="text-align: center;">No products found</td>
                                    </tr>
                                <% } else { %>
                                    <% products.forEach(product => { %>
                                        <tr>
                                            <td>
                                                <% if (product.productImage && product.productImage.length > 0) { %>
                                                    <img src="/uploads/product-images/<%= product.productImage[0] %>" class="product-img" alt="<%= product.productName %>">
                                                <% } else { %>
                                                    <div class="product-img bg-light d-flex align-items-center justify-content-center">
                                                        <i class="bi bi-image text-muted"></i>
                                                    </div>
                                                <% } %>
                                            </td>
                                            <td><%= product.productName || 'N/A' %></td>
                                            <td><%= product.category && product.category.name ? product.category.name : 'N/A' %></td>
                                            <td>$<%= product.price ? product.price.toFixed(2) : '0.00' %></td>
                                            <td><%= product.quantity ?? '0' %></td>
                                            <td>
                                                <span class="status-<%= product.status.toLowerCase().replace(' ', '-') %>">
                                                    <i class="bi bi-circle-fill"></i> <%= product.status %>
                                                </span>
                                                <% if (product.isBlocked) { %>
                                                    <span class="badge bg-danger ms-2">Blocked (Hidden from users)</span>
                                                <% } %>
                                            </td>
                                            <td class="action-btns">
                                                <a href="/admin/edit-product/<%= product._id %>" class="btn btn-sm btn-outline-primary me-1">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <a href="/admin/block-product/<%= product._id %>" class="btn btn-sm <%= product.isBlocked ? 'btn-success' : 'btn-warning' %> me-1" title="<%= product.isBlocked ? 'Unblock Product' : 'Block Product' %>">
                                                    <i class="bi <%= product.isBlocked ? 'bi-check-circle' : 'bi-slash-circle' %>"></i>
                                                    <%= product.isBlocked ? 'Unblock' : 'Block' %>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteProduct('<%= product._id %>')">
                                                    <i class="bi bi-trash"></i> Delete
                                                </button>
                                            </td>
                                        </tr>
                                    <% }) %>
                                <% } %>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Pagination Controls -->
            <% if (totalPages > 1 && typeof currentPage !== 'undefined') { %>
                <div class="pagination" id="paginationControls">
                    <button class="pagination-btn <%= currentPage === 1 ? 'disabled' : '' %>"
                            onclick="loadPage(<%= Math.max(1, currentPage - 1) %>)"
                            <% if (currentPage === 1) { %> disabled <% } %>>
                        Previous
                    </button>

                    <% for (let i = 1; i <= totalPages; i++) { %>
                        <button class="pagination-btn <%= currentPage === i ? 'active' : '' %>"
                                onclick="loadPage(<%= i %>)">
                            <%= i %>
                        </button>
                    <% } %>

                    <button class="pagination-btn <%= currentPage === totalPages ? 'disabled' : '' %>"
                            onclick="loadPage(<%= Math.min(totalPages, currentPage + 1) %>)"
                            <% if (currentPage === totalPages) { %> disabled <% } %>>
                        Next
                    </button>
                </div>
            <% } else if (typeof currentPage === 'undefined') { %>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    Error: Unable to load pagination. Please try again.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <% } %>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Global variables for search and filtering
        let searchTimeout;
        let currentFilters = {
            search: '<%= search || "" %>',
            category: '<%= category || "" %>',
            page: <%= currentPage || 1 %>
        };

        // Debounce function
        function debounce(func, wait) {
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(searchTimeout);
                    func(...args);
                };
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(later, wait);
            };
        }

        // Search validation function
        function validateSearchInput(searchValue) {
            // Allow empty search (show all products)
            if (!searchValue) {
                return { isValid: true, message: '' };
            }

            // Check if input contains only spaces (before trimming)
            if (/^\s+$/.test(searchValue)) {
                return { isValid: false, message: 'Search cannot contain only spaces' };
            }

            const trimmedValue = searchValue.trim();

            // After trimming, if empty, it's valid (same as empty search)
            if (trimmedValue === '') {
                return { isValid: true, message: '' };
            }

            // Check if input contains at least one alphabetic character
            if (!/[a-zA-Z]/.test(trimmedValue)) {
                return { isValid: false, message: 'Please enter a valid search keyword with at least one letter' };
            }

            // Check minimum length (at least 2 characters after trimming)
            if (trimmedValue.length < 2) {
                return { isValid: false, message: 'Search keyword must be at least 2 characters long' };
            }

            return { isValid: true, message: '' };
        }

        // Update reset button state
        function updateResetButtonState() {
            const searchInput = document.getElementById('searchInput');
            const categoryFilter = document.getElementById('categoryFilter');
            const resetButton = document.getElementById('resetFilters');
            const searchError = document.getElementById('searchError');

            const searchValue = searchInput.value;
            const categoryValue = categoryFilter.value;
            const validation = validateSearchInput(searchValue);

            // Show/hide error message
            if (!validation.isValid) {
                searchError.textContent = validation.message;
                searchError.style.display = 'block';
                searchInput.classList.add('is-invalid');
            } else {
                searchError.style.display = 'none';
                searchInput.classList.remove('is-invalid');
            }

            // Enable reset button if there are any filters or valid search
            const hasFilters = (validation.isValid && searchValue.trim() !== '') || categoryValue.trim() !== '';
            resetButton.disabled = !hasFilters;
        }

        // Search and filter products
        async function searchAndFilterProducts(resetPage = true) {
            try {
                const searchInput = document.getElementById('searchInput');
                const categoryFilter = document.getElementById('categoryFilter');
                const searchInfo = document.getElementById('searchInfo');
                const searchInfoText = document.getElementById('searchInfoText');
                const tableLoading = document.getElementById('tableLoading');
                const tableContainer = document.querySelector('.table-responsive');

                // Validate search input
                const searchValue = searchInput.value;
                const validation = validateSearchInput(searchValue);

                if (!validation.isValid) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Invalid Search',
                        text: validation.message,
                        confirmButtonColor: '#dc3545'
                    });
                    return;
                }

                // Update current filters
                currentFilters.search = searchInput.value.trim();
                currentFilters.category = categoryFilter.value;
                if (resetPage) currentFilters.page = 1;

                // Show loading state
                searchInput.classList.add('search-loading');
                tableLoading.style.display = 'block';
                tableContainer.style.opacity = '0.5';

                // Build query parameters
                const params = new URLSearchParams();
                if (currentFilters.search) params.append('search', currentFilters.search);
                if (currentFilters.category) params.append('category', currentFilters.category);
                params.append('page', currentFilters.page);

                const response = await fetch(`/admin/products?${params.toString()}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                // Remove loading state
                searchInput.classList.remove('search-loading');
                tableLoading.style.display = 'none';
                tableContainer.style.opacity = '1';

                if (!response.ok) {
                    throw new Error('Failed to fetch products');
                }

                const data = await response.json();

                // Update table
                updateProductTable(data.products);

                // Update pagination
                updatePagination(data);

                // Update search info
                updateSearchInfo(data, searchInfo, searchInfoText);

                // Update URL without page reload
                const newUrl = `/admin/products?${params.toString()}`;
                window.history.replaceState({}, '', newUrl);

            } catch (error) {
                console.error('Error searching products:', error);
                document.getElementById('searchInput').classList.remove('search-loading');
                document.getElementById('tableLoading').style.display = 'none';
                document.querySelector('.table-responsive').style.opacity = '1';

                Swal.fire({
                    icon: 'error',
                    title: 'Search Error',
                    text: 'Error searching products. Please try again.',
                    confirmButtonColor: '#dc3545',
                    confirmButtonText: 'OK'
                });
            }
        }

        // Update product table
        function updateProductTable(products) {
            const tbody = document.getElementById('productTableBody');
            tbody.innerHTML = '';

            if (products.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 7;
                cell.style.textAlign = 'center';
                cell.textContent = 'No products found';
                row.appendChild(cell);
                tbody.appendChild(row);
                return;
            }

            products.forEach(product => {
                const row = document.createElement('tr');

                // Image
                const imageCell = document.createElement('td');
                if (product.productImage && product.productImage.length > 0) {
                    const img = document.createElement('img');
                    img.src = `/uploads/product-images/${product.productImage[0]}`;
                    img.className = 'product-img';
                    img.alt = product.productName || 'Product Image';
                    imageCell.appendChild(img);
                } else {
                    const div = document.createElement('div');
                    div.className = 'product-img bg-light d-flex align-items-center justify-content-center';
                    const icon = document.createElement('i');
                    icon.className = 'bi bi-image text-muted';
                    div.appendChild(icon);
                    imageCell.appendChild(div);
                }
                row.appendChild(imageCell);

                // Name (with validation)
                const nameCell = document.createElement('td');
                const name = product.productName || 'N/A';
                nameCell.textContent = validateProductName(name) ? name : 'Invalid Name';
                if (!validateProductName(name)) {
                    nameCell.style.color = 'red';
                }
                row.appendChild(nameCell);

                // Category
                const categoryCell = document.createElement('td');
                categoryCell.textContent = product.category && product.category.name ? product.category.name : 'N/A';
                row.appendChild(categoryCell);

                // Price
                const priceCell = document.createElement('td');
                priceCell.textContent = product.price ? `$${product.price.toFixed(2)}` : '$0.00';
                row.appendChild(priceCell);

                // Quantity
                const quantityCell = document.createElement('td');
                quantityCell.textContent = product.quantity ?? '0';
                row.appendChild(quantityCell);

                // Status
                const statusCell = document.createElement('td');
                const statusSpan = document.createElement('span');
                statusSpan.className = `status-${product.status.toLowerCase().replace(' ', '-')}`;
                const statusIcon = document.createElement('i');
                statusIcon.className = 'bi bi-circle-fill';
                statusSpan.appendChild(statusIcon);
                statusSpan.appendChild(document.createTextNode(` ${product.status}`));
                statusCell.appendChild(statusSpan);
                if (product.isBlocked) {
                    const badge = document.createElement('span');
                    badge.className = 'badge bg-danger ms-2';
                    badge.textContent = 'Blocked (Hidden from users)';
                    statusCell.appendChild(badge);
                }
                row.appendChild(statusCell);

                // Actions
                const actionCell = document.createElement('td');
                actionCell.className = 'action-btns';

                const editLink = document.createElement('a');
                editLink.href = `/admin/edit-product/${product._id}`;
                editLink.className = 'btn btn-sm btn-outline-primary me-1';
                const editIcon = document.createElement('i');
                editIcon.className = 'bi bi-pencil';
                editLink.appendChild(editIcon);
                actionCell.appendChild(editLink);

                const toggleLink = document.createElement('a');
                toggleLink.href = `/admin/block-product/${product._id}`;
                toggleLink.className = `btn btn-sm ${product.isBlocked ? 'btn-success' : 'btn-warning'} me-1`;
                toggleLink.title = product.isBlocked ? 'Unblock Product' : 'Block Product';
                const toggleIcon = document.createElement('i');
                toggleIcon.className = `bi ${product.isBlocked ? 'bi-check-circle' : 'bi-slash-circle'}`;
                toggleLink.appendChild(toggleIcon);
                toggleLink.appendChild(document.createTextNode(product.isBlocked ? ' Unblock' : ' Block'));
                actionCell.appendChild(toggleLink);

                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'btn btn-sm btn-outline-danger';
                deleteBtn.onclick = () => deleteProduct(product._id);
                const deleteIcon = document.createElement('i');
                deleteIcon.className = 'bi bi-trash';
                deleteBtn.appendChild(deleteIcon);
                deleteBtn.appendChild(document.createTextNode(' Delete'));
                actionCell.appendChild(deleteBtn);

                row.appendChild(actionCell);
                tbody.appendChild(row);
            });
        }

        // Update pagination
        function updatePagination(data) {
            const pagination = document.getElementById('paginationControls');
            if (!pagination) return;

            pagination.innerHTML = '';

            if (data.totalPages <= 1) {
                pagination.style.display = 'none';
                return;
            }

            pagination.style.display = 'flex';

            const prevBtn = document.createElement('button');
            prevBtn.className = `pagination-btn ${data.currentPage === 1 ? 'disabled' : ''}`;
            prevBtn.disabled = data.currentPage === 1;
            prevBtn.onclick = () => {
                currentFilters.page = Math.max(1, data.currentPage - 1);
                searchAndFilterProducts(false);
            };
            prevBtn.textContent = 'Previous';
            pagination.appendChild(prevBtn);

            for (let i = 1; i <= data.totalPages; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `pagination-btn ${data.currentPage === i ? 'active' : ''}`;
                pageBtn.onclick = () => {
                    currentFilters.page = i;
                    searchAndFilterProducts(false);
                };
                pageBtn.textContent = i;
                pagination.appendChild(pageBtn);
            }

            const nextBtn = document.createElement('button');
            nextBtn.className = `pagination-btn ${data.currentPage === data.totalPages ? 'disabled' : ''}`;
            nextBtn.disabled = data.currentPage === data.totalPages;
            nextBtn.onclick = () => {
                currentFilters.page = Math.min(data.totalPages, data.currentPage + 1);
                searchAndFilterProducts(false);
            };
            nextBtn.textContent = 'Next';
            pagination.appendChild(nextBtn);
        }

        // Update search info
        function updateSearchInfo(data, searchInfo, searchInfoText) {
            const hasFilters = currentFilters.search || currentFilters.category;

            if (hasFilters) {
                let infoText = `Found ${data.products.length} product${data.products.length !== 1 ? 's' : ''}`;

                if (currentFilters.search && currentFilters.category) {
                    infoText += ` matching "${currentFilters.search}" in category "${currentFilters.category}"`;
                } else if (currentFilters.search) {
                    infoText += ` matching "${currentFilters.search}"`;
                } else if (currentFilters.category) {
                    infoText += ` in category "${currentFilters.category}"`;
                }

                searchInfoText.textContent = infoText;
                searchInfo.style.display = 'block';
                searchInfo.classList.add('search-info-fade');
            } else {
                searchInfo.style.display = 'none';
            }
        }

        // Debounced search function
        const debouncedSearch = debounce(searchAndFilterProducts, 500);

        // Initialize event listeners
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            const categoryFilter = document.getElementById('categoryFilter');
            const clearSearch = document.getElementById('clearSearch');
            const resetFilters = document.getElementById('resetFilters');

            // Search input with validation and debouncing
            searchInput.addEventListener('input', function() {
                updateResetButtonState();

                // Add visual feedback for active filter
                if (this.value.trim()) {
                    this.classList.add('filter-active');
                } else {
                    this.classList.remove('filter-active');
                }

                // Only search if validation passes or search is empty
                const validation = validateSearchInput(this.value);
                if (validation.isValid || this.value.trim() === '') {
                    debouncedSearch();
                }
            });

            // Category filter
            categoryFilter.addEventListener('change', function() {
                updateResetButtonState();

                // Add visual feedback for active filter
                if (this.value) {
                    this.classList.add('filter-active');
                } else {
                    this.classList.remove('filter-active');
                }
                searchAndFilterProducts();
            });

            // Clear search
            clearSearch.addEventListener('click', function() {
                searchInput.value = '';
                searchInput.classList.remove('filter-active');
                updateResetButtonState();
                searchAndFilterProducts();
            });

            // Reset all filters
            resetFilters.addEventListener('click', function() {
                searchInput.value = '';
                categoryFilter.value = '';
                searchInput.classList.remove('filter-active');
                categoryFilter.classList.remove('filter-active');
                document.getElementById('searchInfo').style.display = 'none';
                updateResetButtonState();
                searchAndFilterProducts();
            });

            // Allow Enter key to search if valid
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const validation = validateSearchInput(this.value);
                    if (validation.isValid || this.value.trim() === '') {
                        searchAndFilterProducts();
                    } else {
                        Swal.fire({
                            icon: 'warning',
                            title: 'Invalid Search',
                            text: validation.message,
                            confirmButtonColor: '#dc3545'
                        });
                    }
                }
            });

            // Set initial filter states and validation
            if (searchInput.value.trim()) {
                searchInput.classList.add('filter-active');
            }
            if (categoryFilter.value) {
                categoryFilter.classList.add('filter-active');
            }
            updateResetButtonState();
        });

        // Validation function for product name (letters, spaces, and numbers allowed)
        function validateProductName(name) {
            const pattern = /^[A-Za-z0-9\s]+$/;
            return pattern.test(name);
        }

        // Validate displayed product names on page load
        document.addEventListener('DOMContentLoaded', () => {
            const productNameCells = document.querySelectorAll('#productTableBody td:nth-child(2)');
            productNameCells.forEach(cell => {
                const name = cell.textContent.trim();
                if (!validateProductName(name)) {
                    cell.textContent = 'Invalid Name';
                    cell.style.color = 'red';
                }
            });
        });

        // Legacy loadPage function - now uses the new search and filter system
        async function loadPage(page) {
            currentFilters.page = page;
            await searchAndFilterProducts(false);
        }

        // Delete product via AJAX with SweetAlert
        async function deleteProduct(productId) {
            // Get product name for better UX
            const productRow = event.currentTarget.closest('tr');
            const productName = productRow.querySelector('td:nth-child(2)').textContent.trim();

            const result = await Swal.fire({
                title: 'Delete Product?',
                html: `Are you sure you want to delete <strong>"${productName}"</strong>?<br><br>
                       <small class="text-muted">This will move the product to trash. You can restore it later if needed.</small>`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="bi bi-trash"></i> Yes, delete it!',
                cancelButtonText: '<i class="bi bi-x-circle"></i> Cancel',
                reverseButtons: true,
                focusCancel: true
            });

            if (!result.isConfirmed) return;

            try {
                // Show loading state
                Swal.fire({
                    title: 'Deleting Product...',
                    html: `Moving "${productName}" to trash...`,
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                const response = await fetch('/admin/delete-product', {
                    method: 'DELETE',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ id: productId })
                });

                const data = await response.json();

                if (data.success) {
                    await Swal.fire({
                        icon: 'success',
                        title: 'Product Deleted!',
                        html: `<strong>"${data.productName || productName}"</strong> has been moved to trash successfully.<br><br>
                               <small class="text-muted">You can restore it from the deleted products section if needed.</small>`,
                        confirmButtonColor: '#28a745',
                        confirmButtonText: '<i class="bi bi-check-circle"></i> OK',
                        timer: 3000,
                        timerProgressBar: true
                    });

                    // Reload the current page to reflect the deletion
                    searchAndFilterProducts(false);
                } else {
                    await Swal.fire({
                        icon: 'error',
                        title: 'Delete Failed',
                        text: data.message || 'Failed to delete product',
                        confirmButtonColor: '#dc3545',
                        confirmButtonText: '<i class="bi bi-exclamation-circle"></i> OK'
                    });
                }
            } catch (error) {
                console.error('Error deleting product:', error);
                await Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'An unexpected error occurred while deleting the product. Please try again.',
                    confirmButtonColor: '#dc3545',
                    confirmButtonText: '<i class="bi bi-exclamation-circle"></i> OK'
                });
            }
        }
    </script>
</body>
</html>