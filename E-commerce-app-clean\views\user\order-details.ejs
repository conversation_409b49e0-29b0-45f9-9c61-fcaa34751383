<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Order Details - Luxe Scents</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
    :root {
      --primary-color: #6200ea;
      --secondary-color: #f8f9fa;
      --success-color: #28a745;
      --danger-color: #dc3545;
      --warning-color: #ffc107;
      --info-color: #17a2b8;
      --dark-color: #343a40;
      --light-color: #f8f9fa;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f8f9fa;
      color: var(--dark-color);
    }

    .navbar {
      background-color: rgba(0, 0, 0, 0.9);
      padding: 1rem 2rem;
      border-bottom: 1px solid #090909;
      backdrop-filter: blur(10px);
    }
    .navbar-brand {
      font-weight: 700;
      font-size: 1.8rem;
      color: #ffffff !important;
      letter-spacing: 1px;
    }
    .navbar-brand:hover {
      color: #bb86fc !important;
    }
    .nav-link {
      color: #e0e0e0 !important;
      font-weight: 500;
      margin: 0 1rem;
      transition: color 0.3s;
    }
    .nav-link:hover {
      color: #bb86fc !important;
    }
    .navbar-icons {
      display: flex;
      align-items: center;
      gap: 1.5rem;
    }
    .navbar-icons a {
      color: #e0e0e0;
      font-size: 1.2rem;
      position: relative;
      transition: color 0.3s;
    }
    .navbar-icons a:hover {
      color: #bb86fc;
    }
    .badge {
      position: absolute;
      top: -8px;
      right: -8px;
      background-color: #6200ea;
      color: #ffffff;
      font-size: 0.7rem;
      border-radius: 50%;
      padding: 2px 6px;
    }
    .dropdown-menu {
      background-color: #2a2a2a;
      border: 1px solid #444;
    }
    .dropdown-item {
      color: #e0e0e0 !important;
      transition: background-color 0.3s, color 0.3s;
    }
    .dropdown-item:hover {
      background-color: #6200ea;
      color: #ffffff !important;
    }

    .order-container {
      max-width: 1000px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .order-header {
      background: white;
      border-radius: 15px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .order-title {
      font-size: 2rem;
      font-weight: 700;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .order-info {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
      margin-top: 1rem;
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid #e9ecef;
    }

    .info-label {
      font-weight: 600;
      color: #495057;
    }

    .info-value {
      font-weight: 700;
      color: var(--primary-color);
    }

    .status-badge {
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-weight: 600;
      font-size: 0.9rem;
    }

    .status-pending {
      background-color: #fff3cd;
      color: #856404;
    }

    .status-confirmed {
      background-color: #d4edda;
      color: #155724;
    }

    .status-delivered {
      background-color: #d1ecf1;
      color: #0c5460;
    }

    .order-items {
      background: white;
      border-radius: 15px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .section-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--dark-color);
      margin-bottom: 1.5rem;
      border-bottom: 2px solid var(--primary-color);
      padding-bottom: 0.5rem;
    }

    .item-card {
      border: 1px solid #e9ecef;
      border-radius: 10px;
      padding: 1rem;
      margin-bottom: 1rem;
      transition: box-shadow 0.3s ease;
    }

    .item-card:hover {
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .item-details {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .item-image {
      width: 80px;
      height: 80px;
      object-fit: cover;
      border-radius: 8px;
    }

    .item-info {
      flex: 1;
    }

    .item-name {
      font-weight: 600;
      color: var(--dark-color);
      margin-bottom: 0.5rem;
    }

    .item-category {
      color: #6c757d;
      font-size: 0.9rem;
      margin-bottom: 0.5rem;
    }

    .item-price {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .current-price {
      font-weight: 700;
      color: var(--success-color);
    }

    .original-price {
      text-decoration: line-through;
      color: #6c757d;
      font-size: 0.9rem;
    }

    .item-quantity {
      font-weight: 600;
      color: var(--primary-color);
    }

    .back-button {
      background: linear-gradient(135deg, var(--primary-color), #7c4dff);
      border: none;
      border-radius: 50px;
      padding: 0.75rem 2rem;
      font-weight: 600;
      color: white;
      text-decoration: none;
      transition: all 0.3s ease;
      box-shadow: 0 5px 15px rgba(98, 0, 234, 0.3);
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }

    .back-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(98, 0, 234, 0.4);
      color: white;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .order-header,
      .order-items {
        padding: 1.5rem;
        margin: 1rem;
      }

      .order-title {
        font-size: 1.5rem;
      }

      .order-info {
        grid-template-columns: 1fr;
      }

      .item-details {
        flex-direction: column;
        text-align: center;
      }

      .item-image {
        width: 100px;
        height: 100px;
      }
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
      <a class="navbar-brand" href="/">LUXE SCENTS</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <a class="nav-link" href="/">Home</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/shop">Shop</a>
          </li>
        </ul>
        <div class="navbar-icons">
          <a href="/wishlist" class="text-white me-3">
            <i class="fas fa-heart"></i>
            <span class="badge bg-danger" id="wishlistCount">0</span>
          </a>
          <a href="/cart" class="text-white me-3">
            <i class="fas fa-shopping-cart"></i>
            <span class="badge bg-danger" id="cartCount">0</span>
          </a>
          <div class="dropdown">
            <a href="#" class="dropdown-toggle text-white" role="button" data-bs-toggle="dropdown">
              <i class="fas fa-user-circle"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li><a class="dropdown-item" href="/profile">My Profile</a></li>
              <li><a class="dropdown-item" href="/addresses">My Addresses</a></li>
              <li><a class="dropdown-item" href="/orders">My Orders</a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="/logout">Logout</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <div class="order-container">
    <!-- Order Header -->
    <div class="order-header">
      <h1 class="order-title">Order Details</h1>
      <div class="order-info">
        <div class="info-item">
          <span class="info-label">Order Number:</span>
          <span class="info-value"><%= order.orderNumber %></span>
        </div>
        <div class="info-item">
          <span class="info-label">Order Date:</span>
          <span class="info-value"><%= new Date(order.orderDate).toLocaleDateString('en-IN') %></span>
        </div>
        <div class="info-item">
          <span class="info-label">Status:</span>
          <span class="status-badge status-<%= order.orderStatus.toLowerCase() %>"><%= order.orderStatus %></span>
        </div>
        <div class="info-item">
          <span class="info-label">Payment Method:</span>
          <span class="info-value"><%= order.paymentMethod %></span>
        </div>
        <div class="info-item">
          <span class="info-label">Total Amount:</span>
          <span class="info-value">₹<%= order.totalAmount.toFixed(2) %></span>
        </div>
        <div class="info-item">
          <span class="info-label">Expected Delivery:</span>
          <span class="info-value"><%= new Date(order.expectedDelivery).toLocaleDateString('en-IN') %></span>
        </div>
        <% if (order.deliveredAt) { %>
          <div class="info-item">
            <span class="info-label">Delivered On:</span>
            <span class="info-value"><%= new Date(order.deliveredAt).toLocaleDateString('en-IN') %></span>
          </div>
        <% } %>
        <% if (order.cancellationReason) { %>
          <div class="info-item">
            <span class="info-label">Cancellation Reason:</span>
            <span class="info-value"><%= order.cancellationReason %></span>
          </div>
        <% } %>
        <% if (order.returnStatus && order.returnStatus !== 'None') { %>
          <div class="info-item">
            <span class="info-label">Return Status:</span>
            <span class="info-value" style="color:
              <%= order.returnStatus === 'Requested' ? '#856404' :
                  order.returnStatus === 'Approved' ? '#155724' :
                  order.returnStatus === 'Rejected' ? '#721c24' :
                  order.returnStatus === 'Completed' ? '#0c5460' : '#6c757d' %>;">
              <%= order.returnStatus %>
            </span>
          </div>
        <% } %>
        <% if (order.returnReason) { %>
          <div class="info-item">
            <span class="info-label">Return Reason:</span>
            <span class="info-value"><%= order.returnReason %></span>
          </div>
        <% } %>
        <% if (order.returnRequestedAt) { %>
          <div class="info-item">
            <span class="info-label">Return Requested:</span>
            <span class="info-value"><%= new Date(order.returnRequestedAt).toLocaleDateString('en-IN') %></span>
          </div>
        <% } %>
        <% if (order.returnApprovedAt) { %>
          <div class="info-item">
            <span class="info-label">Return Approved:</span>
            <span class="info-value"><%= new Date(order.returnApprovedAt).toLocaleDateString('en-IN') %></span>
          </div>
        <% } %>
        <% if (order.returnRejectedAt) { %>
          <div class="info-item">
            <span class="info-label">Return Rejected:</span>
            <span class="info-value"><%= new Date(order.returnRejectedAt).toLocaleDateString('en-IN') %></span>
          </div>
        <% } %>
        <% if (order.adminReturnNotes) { %>
          <div class="info-item">
            <span class="info-label">Admin Notes:</span>
            <span class="info-value"><%= order.adminReturnNotes %></span>
          </div>
        <% } %>
      </div>
    </div>

    <!-- Order Items -->
    <div class="order-items">
      <h2 class="section-title">Order Items</h2>
      <% order.items.forEach(item => { %>
        <div class="item-card">
          <div class="item-details">
            <img src="/uploads/product-images/<%= item.productImage %>" alt="<%= item.productName %>" class="item-image">
            <div class="item-info">
              <div class="item-name"><%= item.productName %></div>
              <div class="item-category"><%= item.category %></div>
              <div class="item-price">
                <span class="current-price">₹<%= (item.salePrice || item.price).toFixed(2) %></span>
                <% if (item.salePrice && item.salePrice < item.price) { %>
                  <span class="original-price">₹<%= item.price.toFixed(2) %></span>
                <% } %>
              </div>
            </div>
            <div class="item-quantity">
              Qty: <%= item.quantity %>
            </div>
          </div>
        </div>
      <% }); %>
    </div>

    <!-- Order Actions -->
    <div class="text-center mb-4">
      <div class="d-flex justify-content-center gap-3 flex-wrap">
        <a href="/orders/<%= order._id %>/invoice" class="back-button" style="background: linear-gradient(135deg, #28a745, #20c997);">
          <i class="fas fa-download"></i>
          Download Invoice
        </a>

        <% if (order.canBeCancelled) { %>
          <button class="back-button" style="background: linear-gradient(135deg, #dc3545, #e74c3c);" onclick="showCancelModal('<%= order._id %>', '<%= order.orderNumber %>')">
            <i class="fas fa-times"></i>
            Cancel Order
          </button>
        <% } %>

        <% if (order.canRequestReturn) { %>
          <% if (order.returnStatus === 'None' || order.returnStatus === 'Rejected') { %>
            <button class="back-button" style="background: linear-gradient(135deg, #ffc107, #f39c12); color: #212529;" onclick="showReturnModal('<%= order._id %>', '<%= order.orderNumber %>')">
              <i class="fas fa-undo"></i>
              Request Return
            </button>
          <% } %>
        <% } %>

        <% if (order.returnStatus === 'Requested') { %>
          <span class="back-button" style="background: linear-gradient(135deg, #6c757d, #5a6268); cursor: default;">
            <i class="fas fa-clock"></i>
            Return Pending
          </span>
        <% } %>

        <% if (order.returnStatus === 'Approved') { %>
          <span class="back-button" style="background: linear-gradient(135deg, #28a745, #20c997); cursor: default;">
            <i class="fas fa-check"></i>
            Return Approved
          </span>
        <% } %>

        <% if (order.returnStatus === 'Rejected') { %>
          <span class="back-button" style="background: linear-gradient(135deg, #dc3545, #e74c3c); cursor: default;">
            <i class="fas fa-times"></i>
            Return Rejected
          </span>
        <% } %>
      </div>
    </div>

    <!-- Back Button -->
    <div class="text-center">
      <a href="/orders" class="back-button" style="background: linear-gradient(135deg, #6c757d, #5a6268);">
        <i class="fas fa-arrow-left"></i>
        Back to Orders
      </a>
    </div>
  </div>

  <!-- Cancel Order Modal -->
  <div class="modal fade" id="cancelModal" tabindex="-1" aria-labelledby="cancelModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="cancelModalLabel">Cancel Order</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <p>Are you sure you want to cancel order <strong id="cancelOrderNumber"></strong>?</p>
          <div class="mb-3">
            <label for="cancelReason" class="form-label">Reason for cancellation (optional):</label>
            <textarea class="form-control" id="cancelReason" rows="3" placeholder="Please provide a reason for cancellation..."></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Keep Order</button>
          <button type="button" class="btn btn-danger" id="confirmCancel">Cancel Order</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Return Order Modal -->
  <div class="modal fade" id="returnModal" tabindex="-1" aria-labelledby="returnModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="returnModalLabel">Request Return</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <p>Submit return request for order <strong id="returnOrderNumber"></strong>?</p>
          <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            Your return request will be reviewed by our admin team. You will be notified once the request is approved or rejected.
          </div>
          <div class="mb-3">
            <label for="returnReason" class="form-label">Reason for return <span class="text-danger">*</span>:</label>
            <textarea class="form-control" id="returnReason" rows="3" placeholder="Please provide a detailed reason for return..." required></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-warning" id="confirmReturn">Submit Return Request</button>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script>
    let currentOrderId = null;

    // Show cancel modal
    function showCancelModal(orderId, orderNumber) {
      currentOrderId = orderId;
      document.getElementById('cancelOrderNumber').textContent = orderNumber;
      document.getElementById('cancelReason').value = '';
      new bootstrap.Modal(document.getElementById('cancelModal')).show();
    }

    // Show return modal
    function showReturnModal(orderId, orderNumber) {
      currentOrderId = orderId;
      document.getElementById('returnOrderNumber').textContent = orderNumber;
      document.getElementById('returnReason').value = '';
      new bootstrap.Modal(document.getElementById('returnModal')).show();
    }

    // Confirm cancel order
    document.getElementById('confirmCancel').addEventListener('click', async function() {
      if (!currentOrderId) return;

      const reason = document.getElementById('cancelReason').value.trim();

      try {
        const response = await fetch(`/orders/${currentOrderId}/cancel`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            reason: reason,
            items: []
          })
        });

        const result = await response.json();

        if (result.success) {
          Swal.fire({
            icon: 'success',
            title: 'Order Cancelled',
            text: result.message,
            confirmButtonColor: '#6200ea'
          }).then(() => {
            location.reload();
          });
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Cancellation Failed',
            text: result.message,
            confirmButtonColor: '#6200ea'
          });
        }
      } catch (error) {
        console.error('Error cancelling order:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to cancel order. Please try again.',
          confirmButtonColor: '#6200ea'
        });
      }

      bootstrap.Modal.getInstance(document.getElementById('cancelModal')).hide();
    });

    // Confirm return order
    document.getElementById('confirmReturn').addEventListener('click', async function() {
      if (!currentOrderId) return;

      const reason = document.getElementById('returnReason').value.trim();

      if (!reason) {
        Swal.fire({
          icon: 'warning',
          title: 'Return Reason Required',
          text: 'Please provide a reason for return.',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      try {
        const response = await fetch(`/orders/${currentOrderId}/return`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            reason: reason,
            items: []
          })
        });

        const result = await response.json();

        if (result.success) {
          Swal.fire({
            icon: 'success',
            title: 'Return Request Submitted',
            html: `
              <div class="text-center">
                <p>${result.message}</p>
                <div class="mt-3">
                  <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    You will receive a notification once the admin reviews your request.
                  </small>
                </div>
              </div>
            `,
            confirmButtonColor: '#6200ea'
          }).then(() => {
            location.reload();
          });
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Return Request Failed',
            text: result.message,
            confirmButtonColor: '#6200ea'
          });
        }
      } catch (error) {
        console.error('Error processing return:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to process return request. Please try again.',
          confirmButtonColor: '#6200ea'
        });
      }

      bootstrap.Modal.getInstance(document.getElementById('returnModal')).hide();
    });

    // Update cart and wishlist counts
    function updateCounts() {
      fetch('/api/cart/count')
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            const cartBadge = document.getElementById('cartCount');
            if (cartBadge) {
              cartBadge.textContent = data.cartCount;
            }
          }
        })
        .catch(error => {
          console.error('Error updating cart count:', error);
        });

      fetch('/api/wishlist/count')
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            const wishlistBadge = document.getElementById('wishlistCount');
            if (wishlistBadge) {
              wishlistBadge.textContent = data.wishlistCount;
            }
          }
        })
        .catch(error => {
          console.error('Error updating wishlist count:', error);
        });
    }

    // Initialize counts on page load
    document.addEventListener('DOMContentLoaded', function() {
      updateCounts();
    });
  </script>
</body>
</html>
