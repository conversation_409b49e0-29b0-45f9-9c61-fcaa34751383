<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sign Up | Essence Perfume</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
      :root {
        --dark-bg: #121212;
        --form-bg: rgba(26, 26, 26, 0.95);
        --input-bg: #242424;
        --input-focus: #8b7f7f;
        --text-primary: #eddcdc;
        --text-secondary: #909090;
        --accent: #d4af37;
        --accent-hover: #c4a030;
        --error: #dc3545;
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Inter', sans-serif;
        background: url('https://i.pinimg.com/736x/86/7d/cc/867dccfbeb484d59989fa755f68b6c10.jpg') no-repeat center center fixed;
        background-size: cover;
        color: var(--text-primary);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .form-container {
        width: 100%;
        max-width: 450px;
        background-color: var(--form-bg);
        padding: 2rem;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5);
        margin: 1rem;
        position: fixed;
      }

      .form-header {
        margin-bottom: 1.5rem;
        text-align: center;
      }

      .form-title {
        font-weight: 300;
        font-size: 1.75rem;
        color: var(--accent);
        margin-bottom: 0.5rem;
        letter-spacing: 0.5px;
      }

      .form-subtitle {
        color: var(--text-secondary);
        font-size: 0.9rem;
      }

      .input-group {
        position: relative;
        margin-bottom: 1rem;
      }

      .password-container {
        position: relative;
      }

      .password-toggle {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        font-size: 16px;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .password-toggle:hover {
        color: var(--accent);
      }

      .form-control {
        width: 100%;
        background-color: var(--input-bg);
        border: none;
        border-radius: 6px;
        padding: 0.75rem 1rem;
        color: var(--text-primary);
        font-size: 0.9rem;
        transition: all 0.25s ease;
      }

      .form-control:focus {
        background-color: var(--input-focus);
        outline: none;
        box-shadow: none;
        border-left: 2px solid var(--accent);
      }

      .form-control.is-invalid {
        border-left: 2px solid var(--error);
      }

      .form-control::placeholder {
        color: var(--text-secondary);
        opacity: 0.7;
      }

      .error-message {
        color: var(--error);
        font-size: 0.7rem;
        margin-top: 0.25rem;
        display: block;
      }

      .verification-options {
        margin-bottom: 1.5rem;
      }

      .verification-title {
        font-size: 0.85rem;
        margin-bottom: 0.5rem;
        color: var(--text-secondary);
      }

      .form-check {
        display: flex;
        align-items: center;
        margin-bottom: 0.35rem;
      }

      .form-check-input {
        margin-right: 0.5rem;
        cursor: pointer;
      }

      .form-check-input:checked {
        background-color: var(--accent);
        border-color: var(--accent);
      }

      .form-check-label {
        font-size: 0.85rem;
        cursor: pointer;
      }

      .btn {
        padding: 0.75rem;
        border: none;
        border-radius: 6px;
        font-size: 0.9rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.25s ease;
      }

      .btn-primary {
        background-color: var(--accent);
        color: #000;
      }

      .btn-primary:hover {
        background-color: var(--accent-hover);
        transform: translateY(-2px);
      }

      .btn-outline {
        width: 100%;
        background-color: transparent;
        border: 1px solid var(--text-secondary);
        color: var(--text-primary);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0.75rem 0;
      }

      .btn-outline:hover {
        border-color: var(--accent);
        color: var(--accent);
        transform: translateY(-2px);
      }

      .btn-outline img {
        margin-right: 0.5rem;
        width: 20px;
        height: 20px;
      }

      .login-text {
        text-align: center;
        font-size: 0.85rem;
        color: var(--text-secondary);
      }

      .login-link {
        color: var(--accent);
        text-decoration: none;
      }

      .login-link:hover {
        text-decoration: underline;
      }

      .button-group {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
      }

      .btn-half {
        flex: 1;
        width: auto;
      }

      .btn-secondary {
        background-color: transparent;
        border: 1px solid var(--accent);
        color: var(--accent);
      }

      .btn-secondary:hover {
        background-color: var(--accent);
        color: #000;
        transform: translateY(-2px);
      }

      @media (max-width: 576px) {
        .form-container {
          padding: 1.5rem;
          margin: 0.5rem;
        }

        .form-title {
          font-size: 1.5rem;
        }

        .button-group {
          flex-direction: column;
          gap: 0.5rem;
        }

        .btn-half {
          width: 100%;
        }
      }
    </style>
   <!-- <img src="/public/images/vecteezy_cool-cat-wearing-sunglasses-against-blue-background_47023082.jpeg" alt="Cool cat with sunglasses" /> -->
  </head>
  <body>
    <div class="form-container">
      <div class="form-header">
        <h3 class="form-title">Create an account</h3>
        <p class="form-subtitle">Enter your details below</p>
      </div>

      <!-- Server-side error handling -->
      <% if (typeof error !== 'undefined' && error) { %>
        <script>
          document.addEventListener('DOMContentLoaded', function() {
            let errorMessage = '';
            const serverError = '<%= error %>';

            switch(serverError) {
              case 'Phone or Email is already used.':
                errorMessage = 'An account with this email or phone number already exists. Please use different credentials or try logging in.';
                break;
              case 'InvalidMobilenumber.':
                errorMessage = 'Please enter a valid phone number in the format +91XXXXXXXXXX.';
                break;
              case 'Passwords do not match.':
                errorMessage = 'The passwords you entered do not match. Please try again.';
                break;
              case 'All fields are required.':
                errorMessage = 'Please fill in all required fields.';
                break;
              case 'Failed to send verification code. Please try again.':
                errorMessage = 'Failed to send verification code. Please check your email/phone and try again.';
                break;
              case 'Registration failed. Please try again.':
                errorMessage = 'Registration failed. Please check your information and try again.';
                break;
              default:
                errorMessage = serverError;
            }

            Swal.fire({
              icon: 'error',
              title: 'Registration Error',
              text: errorMessage,
              confirmButtonColor: '#d4af37'
            });
          });
        </script>
      <% } %>

      <form action="/register" method="POST">
        <div class="input-group">
          <input type="text" name="userName" class="form-control" placeholder="User Name" required />
          <span class="error-message"></span>
        </div>

        <div class="input-group">
          <input type="tel" name="phone" class="form-control" placeholder="Mobile" required />
          <span class="error-message"></span>
        </div>

        <div class="input-group">
          <input type="email" name="email" class="form-control" placeholder="Email" required />
          <span class="error-message"></span>
        </div>

        <div class="input-group">
          <div class="password-container">
            <input type="password" name="password" id="password" class="form-control" placeholder="Password" minlength="8" maxlength="32" required />
            <button type="button" class="password-toggle" onclick="togglePassword('password')">
              <i class="fas fa-eye" id="passwordIcon"></i>
            </button>
          </div>
          <span class="error-message"></span>
        </div>

        <div class="input-group">
          <div class="password-container">
            <input type="password" name="confirmPassword" id="confirmPassword" class="form-control" placeholder="Confirm Password" required />
            <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
              <i class="fas fa-eye" id="confirmPasswordIcon"></i>
            </button>
          </div>
          <span class="error-message"></span>
        </div>

        <div class="verification-options">
          <p class="verification-title">Verification Method</p>
          <div class="form-check">
            <input class="form-check-input" type="radio" name="verificationMethod" id="emailVerification" value="email" checked>
            <label class="form-check-label" for="emailVerification">
              Email Verification
            </label>
          </div>
          <div class="form-check">
            <input class="form-check-input" type="radio" name="verificationMethod" id="phoneVerification" value="phone">
            <label class="form-check-label" for="phoneVerification">
              Phone Verification
            </label>
          </div>
        </div>

        <div class="button-group">
          <button type="submit" class="btn btn-primary btn-half">Create Account</button>
          <a href="/login" class="btn btn-secondary btn-half">Log In</a>
        </div>

        <a href="/google" class="btn btn-outline">
          <img src="https://img.icons8.com/color/20/000000/google-logo.png" alt="Google" />
          Sign up with Google
        </a>
      </form>

      <p class="login-text">Already have an account? <a href="/login" class="login-link">Log in</a></p>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
  // Password toggle functionality
  function togglePassword(inputId) {
    const passwordInput = document.getElementById(inputId);
    const passwordIcon = document.getElementById(inputId + 'Icon');

    if (passwordInput.type === 'password') {
      passwordInput.type = 'text';
      passwordIcon.classList.remove('fa-eye');
      passwordIcon.classList.add('fa-eye-slash');
    } else {
      passwordInput.type = 'password';
      passwordIcon.classList.remove('fa-eye-slash');
      passwordIcon.classList.add('fa-eye');
    }
  }

  // Check for URL parameters and show SweetAlert for errors
  document.addEventListener('DOMContentLoaded', () => {
    const urlParams = new URLSearchParams(window.location.search);
    const error = urlParams.get('error');

    if (error) {
      let errorMessage = '';
      switch(error) {
        case 'User+already+exists':
        case 'Phone+or+Email+is+already+used.':
          errorMessage = 'An account with this email or phone number already exists. Please use different credentials or try logging in.';
          break;
        case 'Invalid+phone+number':
        case 'InvalidMobilenumber.':
          errorMessage = 'Please enter a valid phone number in the format +91XXXXXXXXXX.';
          break;
        case 'Invalid+email+format':
          errorMessage = 'Please enter a valid email address.';
          break;
        case 'Password+too+short':
          errorMessage = 'Password must be at least 8 characters long.';
          break;
        case 'Passwords+do+not+match':
        case 'Passwords+do+not+match.':
          errorMessage = 'The passwords you entered do not match. Please try again.';
          break;
        case 'Registration+failed':
          errorMessage = 'Registration failed. Please check your information and try again.';
          break;
        case 'All+fields+are+required.':
          errorMessage = 'Please fill in all required fields.';
          break;
        case 'Failed+to+send+verification+code.+Please+try+again.':
          errorMessage = 'Failed to send verification code. Please check your email/phone and try again.';
          break;
        default:
          errorMessage = decodeURIComponent(error.replace(/\+/g, ' '));
      }

      Swal.fire({
        icon: 'error',
        title: 'Registration Error',
        text: errorMessage,
        confirmButtonColor: '#d4af37'
      });

      // Clean URL after showing error
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  });

  document.addEventListener('DOMContentLoaded', () => {
    const form = document.querySelector('form');
    const userName = form.userName;
    const phone = form.phone;
    const email = form.email;
    const password = form.password;
    const confirmPassword = form.confirmPassword;

    // Helper to show error
    function showError(input, message) {
      input.classList.add('is-invalid');
      const errorElement = input.nextElementSibling;
      if (errorElement && errorElement.classList.contains('error-message')) {
        errorElement.textContent = message;
      }
    }

    // Helper to clear error
    function clearError(input) {
      input.classList.remove('is-invalid');
      const errorElement = input.nextElementSibling;
      if (errorElement && errorElement.classList.contains('error-message')) {
        errorElement.textContent = '';
      }
    }

    // Username validation (letters, spaces, and underscores allowed in registration)
    userName.addEventListener('input', () => {
      const value = userName.value.trim();

      // Reset validation state
      clearError(userName);

      if (value.length === 0) {
        return; // Don't validate empty field while typing
      }

      let isValid = true;
      let errorMessage = '';

      // Length validation
      if (value.length < 2) {
        isValid = false;
        errorMessage = 'Username must be at least 2 characters long';
      } else if (value.length > 30) {
        isValid = false;
        errorMessage = 'Username must be less than 30 characters long';
      }
      // Simplified character validation - allow letters, numbers, spaces, and underscores
      else if (!/^[A-Za-z0-9 _]+$/.test(value)) {
        isValid = false;
        errorMessage = 'Username can only contain letters, numbers, spaces, and underscores';
      }
      // Must start with a letter or number
      else if (!/^[A-Za-z0-9]/.test(value)) {
        isValid = false;
        errorMessage = 'Username must start with a letter or number';
      }

      if (!isValid) {
        showError(userName, errorMessage);
      }
    });

    // Phone validation with comprehensive rules
    phone.addEventListener('input', () => {
      const value = phone.value.trim();

      // Reset validation state
      clearError(phone);

      if (value.length === 0) {
        return; // Don't validate empty field while typing
      }

      let isValid = true;
      let errorMessage = '';

      // Check basic format first
      if (!/^\+91\d{10}$/.test(value)) {
        isValid = false;
        errorMessage = 'Phone number must be in format +91XXXXXXXXXX (10 digits after +91)';
      }
      // Check for valid Indian mobile number patterns (more lenient)
      else if (!/^\+91[6-9]\d{9}$/.test(value)) {
        isValid = false;
        errorMessage = 'Please enter a valid Indian mobile number starting with 6, 7, 8, or 9';
      }

      if (!isValid) {
        showError(phone, errorMessage);
      }
    });

    // Email validation with comprehensive rules
    email.addEventListener('input', () => {
      const value = email.value.trim();

      // Reset validation state
      clearError(email);

      if (value.length === 0) {
        return; // Don't validate empty field while typing
      }

      let isValid = true;
      let errorMessage = '';

      // Basic email format validation
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        isValid = false;
        errorMessage = 'Please enter a valid email address';
      }
      // Check for underscores in email (NOT allowed in other forms, but OK in registration)
      else if (value.includes('_')) {
        // Underscores are allowed in registration, but we'll note this for other forms
        // This is just for consistency - no error here
      }
      // Email length validation
      else if (value.length > 254) {
        isValid = false;
        errorMessage = 'Email address is too long (maximum 254 characters)';
      }
      // Local part (before @) validation
      else if (value.split('@')[0].length > 64) {
        isValid = false;
        errorMessage = 'Email username part is too long (maximum 64 characters)';
      }
      // Check for consecutive dots
      else if (/\.{2,}/.test(value)) {
        isValid = false;
        errorMessage = 'Email cannot contain consecutive dots';
      }
      // Check for dots at start or end of local part
      else if (/^\.|\.$|@\.|\.\@/.test(value)) {
        isValid = false;
        errorMessage = 'Email cannot start or end with dots around @ symbol';
      }

      if (!isValid) {
        showError(email, errorMessage);
      }
    });

    // Password validation with comprehensive rules
    password.addEventListener('input', () => {
      const value = password.value;

      // Reset validation state
      clearError(password);

      if (value.length === 0) {
        return; // Don't validate empty field while typing
      }

      let isValid = true;
      let errorMessage = '';

      // Length validation
      if (value.length < 8) {
        isValid = false;
        errorMessage = 'Password must be at least 8 characters long';
      } else if (value.length > 32) {
        isValid = false;
        errorMessage = 'Password must be less than 32 characters long';
      }
      // Simplified password validation - just basic requirements
      else if (!/(?=.*[a-z])/.test(value)) {
        isValid = false;
        errorMessage = 'Password must contain at least one lowercase letter';
      } else if (!/(?=.*[A-Z])/.test(value)) {
        isValid = false;
        errorMessage = 'Password must contain at least one uppercase letter';
      } else if (!/(?=.*\d)/.test(value)) {
        isValid = false;
        errorMessage = 'Password must contain at least one number';
      }

      if (!isValid) {
        showError(password, errorMessage);
      }

      // Also validate confirm password if it has a value
      if (confirmPassword.value) {
        confirmPassword.dispatchEvent(new Event('input'));
      }
    });

    // Confirm password validation
    confirmPassword.addEventListener('input', () => {
      const value = confirmPassword.value;
      const passwordValue = password.value;

      // Reset validation state
      clearError(confirmPassword);

      if (value.length === 0) {
        return; // Don't validate empty field while typing
      }

      let isValid = true;
      let errorMessage = '';

      if (passwordValue !== value) {
        isValid = false;
        errorMessage = 'Passwords do not match';
      }

      if (!isValid) {
        showError(confirmPassword, errorMessage);
      }
    });

    // Add blur event listeners for required field validation
    userName.addEventListener('blur', () => {
      if (userName.value.trim() === '') {
        showError(userName, 'Username is required');
      }
    });

    phone.addEventListener('blur', () => {
      if (phone.value.trim() === '') {
        showError(phone, 'Phone number is required');
      }
    });

    email.addEventListener('blur', () => {
      if (email.value.trim() === '') {
        showError(email, 'Email is required');
      }
    });

    password.addEventListener('blur', () => {
      if (password.value === '') {
        showError(password, 'Password is required');
      }
    });

    confirmPassword.addEventListener('blur', () => {
      if (confirmPassword.value === '') {
        showError(confirmPassword, 'Confirm password is required');
      }
    });

    // Final check on form submit - Use AJAX to handle JSON responses
    form.addEventListener('submit', async (e) => {
      e.preventDefault(); // Prevent default form submission

      // Debug: Log current form values
      console.log('Form submission attempt with values:');
      console.log('Username:', userName.value);
      console.log('Phone:', phone.value);
      console.log('Email:', email.value);
      console.log('Password length:', password.value.length);
      console.log('Confirm Password length:', confirmPassword.value.length);
      console.log('Verification Method:', form.verificationMethod.value);

      // Trigger validation for all inputs
      const inputs = [userName, phone, email, password, confirmPassword];
      inputs.forEach(input => {
        input.dispatchEvent(new Event('blur'));
        input.dispatchEvent(new Event('input'));
      });

      // Wait a moment for validation to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      const errors = form.querySelectorAll('.is-invalid');

      if (errors.length > 0) {
        // Debug: Log which fields have errors
        console.log('Validation errors found:');
        errors.forEach(errorField => {
          const fieldName = errorField.name || errorField.id || 'unknown';
          const errorMessage = errorField.nextElementSibling?.textContent || 'no message';
          console.log(`- ${fieldName}: ${errorMessage}`);
        });

        // Show SweetAlert for validation errors
        Swal.fire({
          icon: 'warning',
          title: 'Validation Error',
          text: 'Please fix the errors in the form before submitting.',
          confirmButtonColor: '#d4af37'
        });
        return;
      }

      // Show loading state
      const submitButton = form.querySelector('button[type="submit"]');
      const originalText = submitButton.textContent;
      submitButton.disabled = true;
      submitButton.textContent = 'Creating Account...';

      try {
        // Create FormData from form and convert to URLSearchParams
        const formData = new FormData(form);
        const urlEncodedData = new URLSearchParams();

        // Convert FormData to URLSearchParams for proper backend parsing
        for (const [key, value] of formData.entries()) {
          urlEncodedData.append(key, value);
        }

        console.log('Sending data:', Object.fromEntries(urlEncodedData.entries()));

        // Submit form via fetch with proper content-type
        const response = await fetch('/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: urlEncodedData
        });

        // Check if response is a redirect (successful registration)
        if (response.redirected) {
          // Show success message and redirect
          Swal.fire({
            icon: 'success',
            title: 'Registration Successful!',
            text: 'Verification code has been sent. Redirecting to verification page...',
            confirmButtonColor: '#d4af37',
            timer: 2000,
            timerProgressBar: true,
            showConfirmButton: false
          }).then(() => {
            window.location.href = response.url;
          });
          return;
        }

        // Try to parse JSON response (for errors)
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const result = await response.json();

          if (result.success === false) {
            // Handle error response with SweetAlert
            let errorMessage = '';

            switch(result.message) {
              case 'Phone or Email is already used.':
                errorMessage = 'An account with this email or phone number already exists. Please use different credentials or try logging in.';
                break;
              case 'InvalidMobilenumber.':
                errorMessage = 'Please enter a valid phone number in the format +91XXXXXXXXXX.';
                break;
              case 'Passwords do not match.':
                errorMessage = 'The passwords you entered do not match. Please try again.';
                break;
              case 'All fields are required.':
                errorMessage = 'Please fill in all required fields.';
                break;
              case 'Failed to send verification code. Please try again.':
                errorMessage = 'Failed to send verification code. Please check your email/phone and try again.';
                break;
              case 'No form data received. Please try again.':
                errorMessage = 'Form data was not received properly. Please refresh the page and try again.';
                break;
              default:
                errorMessage = result.message || 'Registration failed. Please try again.';
            }

            Swal.fire({
              icon: 'error',
              title: 'Registration Error',
              text: errorMessage,
              confirmButtonColor: '#d4af37'
            });
          } else if (result.success === true) {
            // Handle success response
            Swal.fire({
              icon: 'success',
              title: 'Registration Successful!',
              text: 'Verification code has been sent. Redirecting to verification page...',
              confirmButtonColor: '#d4af37',
              timer: 2000,
              timerProgressBar: true,
              showConfirmButton: false
            }).then(() => {
              if (result.redirectUrl) {
                window.location.href = result.redirectUrl;
              } else {
                // Fallback redirect
                const email = encodeURIComponent(urlEncodedData.get('email'));
                const phone = encodeURIComponent(urlEncodedData.get('phone'));
                const method = encodeURIComponent(urlEncodedData.get('verificationMethod'));
                window.location.href = `/otp-verification?email=${email}&phone=${phone}&method=${method}`;
              }
            });
          }
        } else {
          // Handle non-JSON response
          const text = await response.text();
          if (text.includes('otp-verification') || response.url.includes('otp-verification')) {
            // Successful registration
            Swal.fire({
              icon: 'success',
              title: 'Registration Successful!',
              text: 'Verification code has been sent. Redirecting to verification page...',
              confirmButtonColor: '#d4af37',
              timer: 2000,
              timerProgressBar: true,
              showConfirmButton: false
            }).then(() => {
              window.location.href = response.url;
            });
          } else {
            throw new Error('Unexpected response format');
          }
        }
      } catch (error) {
        console.error('Registration error:', error);

        Swal.fire({
          icon: 'error',
          title: 'Network Error',
          text: 'Unable to connect to the server. Please check your internet connection and try again.',
          confirmButtonColor: '#d4af37'
        });
      } finally {
        // Reset button state
        submitButton.disabled = false;
        submitButton.textContent = originalText;
      }
    });
  });
</script>
  </body>
</html>