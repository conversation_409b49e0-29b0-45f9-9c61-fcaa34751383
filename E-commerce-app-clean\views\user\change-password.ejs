<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Change Password | Luxe Scents</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    body {
      background-color: #f8f9fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .navbar {
      background-color: #000000;
      padding: 1rem 2rem;
      border-bottom: 1px solid #090909;
    }
    .navbar-brand {
      font-weight: 700;
      font-size: 1.8rem;
      color: #ffffff !important;
      letter-spacing: 1px;
    }
    .nav-link {
      color: #e0e0e0 !important;
      font-weight: 500;
      margin: 0 1rem;
      transition: color 0.3s;
    }
    .nav-link:hover {
      color: #bb86fc !important;
    }
    .password-container {
      max-width: 500px;
      margin: 3rem auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    .password-header {
      background: linear-gradient(135deg, #6200ea, #bb86fc);
      color: white;
      padding: 2rem;
      text-align: center;
    }
    .password-body {
      padding: 2rem;
    }
    .form-control {
      border-radius: 10px;
      border: 2px solid #e9ecef;
      padding: 0.75rem 1rem;
      transition: all 0.3s;
    }
    .form-control:focus {
      border-color: #6200ea;
      box-shadow: 0 0 0 0.2rem rgba(98, 0, 234, 0.25);
    }
    .btn-primary {
      background-color: #6200ea;
      border-color: #6200ea;
      border-radius: 25px;
      padding: 0.75rem 2rem;
      font-weight: 500;
      transition: all 0.3s;
      width: 100%;
    }
    .btn-primary:hover {
      background-color: #5300d1;
      transform: translateY(-2px);
    }
    .btn-secondary {
      border-radius: 25px;
      padding: 0.75rem 2rem;
      font-weight: 500;
      transition: all 0.3s;
      width: 100%;
    }
    .btn-secondary:hover {
      transform: translateY(-2px);
    }
    .alert {
      border-radius: 10px;
      border: none;
    }
    .password-requirements {
      background-color: #f8f9fa;
      padding: 1rem;
      border-radius: 10px;
      margin-bottom: 1rem;
      font-size: 0.9rem;
    }
    .password-requirements ul {
      margin-bottom: 0;
      padding-left: 1.2rem;
    }
    .password-requirements li {
      margin-bottom: 0.3rem;
    }
    .input-group {
      position: relative;
    }
    .password-toggle {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      color: #6c757d;
      cursor: pointer;
      z-index: 10;
    }
    .password-toggle:hover {
      color: #6200ea;
    }
    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 2rem;
    }
    @media (max-width: 768px) {
      .password-container {
        margin: 1rem;
      }
      .action-buttons {
        flex-direction: column;
      }
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
      <a class="navbar-brand" href="/">LUXE SCENTS</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <a class="nav-link" href="/">Home</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/shop">Shop</a>
          </li>
          <li class="nav-item">
            <a class="nav-link active" href="/profile">Profile</a>
          </li>
        </ul>
        <div class="navbar-icons">
          <div class="dropdown">
            <a href="#" class="dropdown-toggle text-white" role="button" data-bs-toggle="dropdown">
              <i class="fas fa-user-circle"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li><a class="dropdown-item" href="/profile">My Profile</a></li>
              <li><a class="dropdown-item" href="#" onclick="confirmLogout(event)">Logout</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- Change Password Container -->
  <div class="password-container">
    <!-- Header -->
    <div class="password-header">
      <h2><i class="fas fa-lock me-2"></i>Change Password</h2>
      <p class="mb-0">Update your account password for better security</p>
    </div>

    <!-- Body -->
    <div class="password-body">
      <!-- Success/Error Messages -->
      <% if (message) { %>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
          <%= message %>
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      <% } %>
      <% if (error) { %>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
          <%= error %>
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      <% } %>

      <!-- Password Requirements -->
      <div class="password-requirements">
        <h6><i class="fas fa-info-circle me-2"></i>Password Requirements</h6>
        <ul>
          <li>At least 8 characters long</li>
          <li>Mix of uppercase and lowercase letters recommended</li>
          <li>Include numbers and special characters for better security</li>
        </ul>
      </div>

      <form action="/profile/change-password" method="POST" id="changePasswordForm">
        <!-- Current Password -->
        <div class="mb-3">
          <label for="currentPassword" class="form-label">Current Password</label>
          <div class="input-group">
            <input type="password" class="form-control" id="currentPassword" name="currentPassword" required>
            <button type="button" class="password-toggle" onclick="togglePassword('currentPassword')">
              <i class="fas fa-eye" id="currentPasswordIcon"></i>
            </button>
          </div>
        </div>

        <!-- New Password -->
        <div class="mb-3">
          <label for="newPassword" class="form-label">New Password</label>
          <div class="input-group">
            <input type="password" class="form-control" id="newPassword" name="newPassword" required minlength="8">
            <button type="button" class="password-toggle" onclick="togglePassword('newPassword')">
              <i class="fas fa-eye" id="newPasswordIcon"></i>
            </button>
          </div>
        </div>

        <!-- Confirm New Password -->
        <div class="mb-3">
          <label for="confirmPassword" class="form-label">Confirm New Password</label>
          <div class="input-group">
            <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required minlength="8">
            <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
              <i class="fas fa-eye" id="confirmPasswordIcon"></i>
            </button>
          </div>
          <div class="form-text" id="passwordMatch"></div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-2"></i>Change Password
          </button>
          <a href="/profile" class="btn btn-secondary">
            <i class="fas fa-times me-2"></i>Cancel
          </a>
        </div>
      </form>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    function confirmLogout(event) {
      event.preventDefault();
      Swal.fire({
        title: 'Are you sure?',
        text: 'You will be logged out of your account',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#6200ea',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, logout'
      }).then((result) => {
        if (result.isConfirmed) {
          window.location.href = '/logout';
        }
      });
    }

    function togglePassword(fieldId) {
      const field = document.getElementById(fieldId);
      const icon = document.getElementById(fieldId + 'Icon');

      if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
      } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
      }
    }

    // Password confirmation validation
    document.getElementById('confirmPassword').addEventListener('input', function() {
      const newPassword = document.getElementById('newPassword').value;
      const confirmPassword = this.value;
      const matchDiv = document.getElementById('passwordMatch');

      if (confirmPassword === '') {
        matchDiv.textContent = '';
        matchDiv.className = 'form-text';
      } else if (newPassword === confirmPassword) {
        matchDiv.textContent = '✓ Passwords match';
        matchDiv.className = 'form-text text-success';
      } else {
        matchDiv.textContent = '✗ Passwords do not match';
        matchDiv.className = 'form-text text-danger';
      }
    });

    // Enhanced form validation with comprehensive password rules
    document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
      const currentPassword = document.getElementById('currentPassword').value;
      const newPassword = document.getElementById('newPassword').value;
      const confirmPassword = document.getElementById('confirmPassword').value;

      // Current password validation
      if (!currentPassword) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Current Password Required',
          text: 'Please enter your current password',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Check for underscores in current password (not allowed)
      if (currentPassword.includes('_')) {
        e.preventDefault();
        Swal.fire({
          icon: 'error',
          title: 'Invalid Password Format',
          text: 'Passwords with underscores are not supported. Please contact support if needed.',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // New password validation
      if (!newPassword) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'New Password Required',
          text: 'Please enter a new password',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      if (newPassword.length < 8) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Password Too Short',
          text: 'New password must be at least 8 characters long',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      if (newPassword.length > 128) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Password Too Long',
          text: 'New password must be less than 128 characters long',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Check for spaces in new password
      if (/\s/.test(newPassword)) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Invalid Password Format',
          text: 'Password cannot contain spaces',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Check for underscores in new password (not allowed)
      if (newPassword.includes('_')) {
        e.preventDefault();
        Swal.fire({
          icon: 'error',
          title: 'Invalid Password Format',
          text: 'New password cannot contain underscores. Please use other special characters like @$!%*?&-',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Password strength validation
      if (!/(?=.*[a-z])/.test(newPassword)) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Weak Password',
          text: 'New password must contain at least one lowercase letter',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      if (!/(?=.*[A-Z])/.test(newPassword)) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Weak Password',
          text: 'New password must contain at least one uppercase letter',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      if (!/(?=.*\d)/.test(newPassword)) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Weak Password',
          text: 'New password must contain at least one number',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      if (!/(?=.*[@$!%*?&-])/.test(newPassword)) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Weak Password',
          text: 'New password must contain at least one special character (@$!%*?&-)',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Check for consecutive identical characters
      if (/(.)\1{2,}/.test(newPassword)) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Weak Password',
          text: 'Password cannot contain more than 2 consecutive identical characters',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Check if new password is same as current password
      if (currentPassword === newPassword) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Same Password',
          text: 'New password must be different from your current password',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Confirm password validation
      if (!confirmPassword) {
        e.preventDefault();
        Swal.fire({
          icon: 'warning',
          title: 'Confirm Password Required',
          text: 'Please confirm your new password',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      if (newPassword !== confirmPassword) {
        e.preventDefault();
        Swal.fire({
          icon: 'error',
          title: 'Password Mismatch',
          text: 'New passwords do not match. Please check and try again.',
          confirmButtonColor: '#6200ea'
        });
        return;
      }
    });
  </script>
</body>
</html>
