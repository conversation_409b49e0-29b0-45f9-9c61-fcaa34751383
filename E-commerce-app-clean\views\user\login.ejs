<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, private">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
    <title>Login</title>

    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: Arial, sans-serif;
        }

        body {
            background-color: #1a1a1a;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: #e0e0e0;
        }

        .container {
            display: flex;
            background-color: #2a2a2a;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
            overflow: hidden;
            max-width: 800px;
            width: 100%;
        }

        .image-box {
            flex: 1;
            background: url('https://i.pinimg.com/736x/d0/9d/b1/d09db174bf0ad9ab3bcd58600cb042db.jpg') no-repeat center center;
            background-size: cover;
            min-height: 400px;
        }

        .form-box {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        h2 {
            font-size: 28px;
            margin-bottom: 10px;
            color: #ffffff;
        }

        p {
            color: #b0b0b0;
            margin-bottom: 20px;
        }

        .error {
            color: #ff5555;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            font-size: 14px;
            margin-bottom: 5px;
            color: #e0e0e0;
        }

        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #444;
            border-radius: 5px;
            background-color: #333;
            color: #e0e0e0;
            font-size: 16px;
        }

        input:focus {
            outline: none;
            border-color: #6200ea;
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #b0b0b0;
            cursor: pointer;
            font-size: 16px;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .password-toggle:hover {
            color: #6200ea;
        }

        .validation-error {
            color: #ff5555;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }

        .forgot {
            text-align: right;
            margin-bottom: 15px;
        }

        .forgot a {
            color: #bb86fc;
            text-decoration: none;
            font-size: 14px;
        }

        .forgot a:hover {
            text-decoration: underline;
        }

        button {
            width: 100%;
            padding: 12px;
            background-color: #6200ea;
            border: none;
            border-radius: 5px;
            color: #ffffff;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #3700b3;
        }

        .google-btn {
            width: 100%;
            padding: 12px;
            margin-top: 10px;
            background-color: #333;
            border: 1px solid #444;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s;
        }

        .google-btn:hover {
            background-color: #444;
        }

        .g-text {
            color: #e0e0e0;
            text-decoration: none;
            font-size: 16px;
            display: flex;
            align-items: center;
        }

        .google-btn img {
            width: 18px;
            margin-right: 10px;
        }

        .signup {
            margin-top: 20px;
            text-align: center;
            font-size: 14px;
            color: #b0b0b0;
        }

        .signup a {
            color: #bb86fc;
            text-decoration: none;
        }

        .signup a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }

            .image-box {
                min-height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="image-box"></div>
        <div class="form-box">
            <h2>Log in to your account</h2>
            <p>Enter your details below</p>
            <% if (typeof error !== 'undefined' && error && error !== 'Invalid email or password') { %>
                <!-- Show server-side error with SweetAlert2 -->
                <script>
                    Swal.fire({
                        icon: 'error',
                        title: 'Login Error',
                        text: '<%= error %>',
                        confirmButtonColor: '#6200ea'
                    });
                </script>
            <% } %>
            <form id="loginForm" action="/login" method="POST">
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" required>
                    <div id="emailError" class="validation-error">Please enter a valid email address</div>
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="password-container">
                        <input type="password" id="password" name="password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                            <i class="fas fa-eye" id="passwordIcon"></i>
                        </button>
                    </div>
                    <div id="passwordError" class="validation-error">Password must be at least 6 characters long</div>
                </div>
                <div class="forgot"><a href="/forgot-password">Forget Password?</a></div>
                <button type="button" onclick="confirmLogin()">Login</button>
                <div class="google-btn">
                    <a href="/google" class="g-text">
                        <img src="https://img.icons8.com/color/16/000000/google-logo.png" alt="Google Logo">
                        sign up with google
                    </a>
                </div>
            </form>
            <div class="signup">
                Don't have an account? <a href="/register">Sign Up</a>
            </div>
        </div>
    </div>

    <script>
        // Password toggle functionality
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const passwordIcon = document.getElementById(inputId + 'Icon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.classList.remove('fa-eye');
                passwordIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                passwordIcon.classList.remove('fa-eye-slash');
                passwordIcon.classList.add('fa-eye');
            }
        }

        // Enhanced client-side form validation with SweetAlert
        function validateForm() {
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;

            // Reset error messages
            document.getElementById('emailError').style.display = 'none';
            document.getElementById('passwordError').style.display = 'none';

            // Email validation with underscore check
            if (!email) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Email Required',
                    text: 'Please enter your email address',
                    confirmButtonColor: '#6200ea'
                });
                return false;
            }

            // Check for underscores in email (not allowed in login)
            if (email.includes('_')) {
                Swal.fire({
                    icon: 'error',
                    title: 'Invalid Email Format',
                    text: 'Email addresses with underscores are not allowed in login. Please use your email without underscores.',
                    confirmButtonColor: '#6200ea'
                });
                return false;
            }

            // Basic email format validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Invalid Email Format',
                    text: 'Please enter a valid email address',
                    confirmButtonColor: '#6200ea'
                });
                return false;
            }

            // Email length validation
            if (email.length > 254) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Email Too Long',
                    text: 'Email address is too long (maximum 254 characters)',
                    confirmButtonColor: '#6200ea'
                });
                return false;
            }

            // Check for consecutive dots in email
            if (/\.{2,}/.test(email)) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Invalid Email Format',
                    text: 'Email cannot contain consecutive dots',
                    confirmButtonColor: '#6200ea'
                });
                return false;
            }

            // Password validation
            if (!password) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Password Required',
                    text: 'Please enter your password',
                    confirmButtonColor: '#6200ea'
                });
                return false;
            }

            if (password.length < 6) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Password Too Short',
                    text: 'Password must be at least 6 characters long',
                    confirmButtonColor: '#6200ea'
                });
                return false;
            }

            // Check for spaces in password
            if (/\s/.test(password)) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Invalid Password Format',
                    text: 'Password cannot contain spaces',
                    confirmButtonColor: '#6200ea'
                });
                return false;
            }

            return true;
        }

        // Confirm login with SweetAlert2
        function confirmLogin() {
            if (!validateForm()) {
                // Show validation error with SweetAlert2
                Swal.fire({
                    icon: 'warning',
                    title: 'Validation Error',
                    text: 'Please fix the errors in the form.',
                    confirmButtonColor: '#6200ea'
                });
                return;
            }

            // Show confirmation dialog
            Swal.fire({
                title: 'Are you sure?',
                text: 'Do you want to log in with these credentials?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#6200ea',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, log in!',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading state
                    Swal.fire({
                        title: 'Logging in...',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Submit the form via AJAX
                    const form = document.getElementById('loginForm');
                    const formData = new FormData(form);
                    const data = Object.fromEntries(formData); // Convert FormData to object

                    // Debug logs
                    console.log('Form Data Object:', data);
                    console.log('JSON Body:', JSON.stringify(data));

                    fetch(form.action, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json' // Set Content-Type to JSON
                        },
                        body: JSON.stringify(data) // Send as JSON
                    })
                    .then(response => {
                        console.log('Response Status:', response.status); // Debug log
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Success!',
                                text: 'Login successful! Redirecting...',
                                confirmButtonColor: '#6200ea',
                                timer: 1500,
                                timerProgressBar: true
                            }).then(() => {
                                window.location.href = '/home';
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Login Failed',
                                text: data.message || 'Invalid credentials.',
                                confirmButtonColor: '#6200ea'
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Fetch Error:', error);
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'An unexpected error occurred. Please try again.',
                            confirmButtonColor: '#6200ea'
                        });
                    });
                }
            });
        }
    </script>

    <!-- No Back Button Prevention Script -->
    <script src="/js/no-back-button.js"></script>
</body>
</html>